#!/bin/bash
set -e

echo "🤖 Deploying MCP Chatbot Interface..."
echo "===================================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "Updating main frontend with chatbot interface"
cd ../frontend-project

# Verify MCPChatbotInterface.js exists
if [ ! -f "src/MCPChatbotInterface.js" ]; then
    echo "❌ Error: MCPChatbotInterface.js not found!"
    echo "Please copy the MCP Chatbot Interface artifact to frontend-project/src/MCPChatbotInterface.js"
    exit 1
fi

# Verify updated App.js exists  
if [ ! -f "src/App.js" ]; then
    echo "❌ Error: Updated App.js not found!"
    echo "Please copy the Updated Frontend App.js artifact to frontend-project/src/App.js"
    exit 1
fi

log_step "Installing additional dependencies"
npm install react-router-dom lucide-react --silent
check_success "React Router installed"

log_step "Building updated frontend"
REACT_APP_AZURE_CLIENT_ID=$APP_ID \
REACT_APP_AZURE_TENANT_ID=$TENANT_ID \
REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net \
npm run build #--silent
check_success "Frontend build completed"

log_step "Deploying updated frontend"
SWA_NAME="${COMPANY_NAME}-${ENVIRONMENT}-frontend"

# Get existing deployment token
DEPLOYMENT_TOKEN=$(az staticwebapp secrets list \
  --name $SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "properties.apiKey" \
  --output tsv 2>/dev/null || echo "")

if [ -z "$DEPLOYMENT_TOKEN" ]; then
    echo "⚠️ Static Web App not found. Creating new one..."
    
    az staticwebapp create \
      --name $SWA_NAME \
      --resource-group $RESOURCE_GROUP \
      --location "centralus" \
      --only-show-errors
    
    DEPLOYMENT_TOKEN=$(az staticwebapp secrets list \
      --name $SWA_NAME \
      --resource-group $RESOURCE_GROUP \
      --query "properties.apiKey" \
      --output tsv)
fi

# Deploy using SWA CLI
sudo swa deploy ./build \
  --deployment-token $DEPLOYMENT_TOKEN \
  --app-location "/"
check_success "Updated frontend deployed"

cd ../scripts

echo "⏱️ Waiting for deployment to propagate (60 seconds)..."
sleep 60

log_step "Testing chatbot interface"
FRONTEND_URL="https://${COMPANY_NAME}-${ENVIRONMENT}-frontend.azurewebsites.net"

# Test main frontend
FRONTEND_RESPONSE=$(curl -o /dev/null -w "%{http_code}" "$FRONTEND_URL" || echo "000")
if [ "$FRONTEND_RESPONSE" = "200" ]; then
    echo "✅ Main frontend accessible (HTTP 200)"
else
    echo "⚠️ Frontend returned HTTP $FRONTEND_RESPONSE"
fi

# Test chatbot route (will return 200 for SPA even if route doesn't exist)
CHATBOT_RESPONSE=$(curl -o /dev/null -w "%{http_code}" "$FRONTEND_URL/chatbot" || echo "000")
if [ "$CHATBOT_RESPONSE" = "200" ]; then
    echo "✅ Chatbot route accessible (HTTP 200)"
else
    echo "⚠️ Chatbot route returned HTTP $CHATBOT_RESPONSE"
fi

log_step "Updating Azure AD redirect URIs for enhanced frontend"
# Get the current frontend URL
SWA_URL=$(az staticwebapp show \
  --name $SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "defaultHostname" \
  --output tsv)

if [ ! -z "$SWA_URL" ]; then
    az ad app update \
      --id $APP_ID \
      --web-redirect-uris "https://$SWA_URL" "https://$SWA_URL/chatbot" "http://localhost:3000" \
      --only-show-errors
    check_success "Azure AD redirect URIs updated for chatbot"
    
    # Update environment config
    echo "export FRONTEND_URL=\"https://$SWA_URL\"" >> ../configs/environment.conf
fi

echo ""
echo "🎉 MCP Chatbot Interface deployment completed!"
echo "Main App: $FRONTEND_URL"
echo "Chatbot: $FRONTEND_URL/chatbot"
echo ""
echo "🔧 Features Added:"
echo "   🤖 12-Step MCP Flow Visualization"
echo "   📊 Real-time Processing Steps"
echo "   🛡️ Policy-Aware Response Filtering"
echo "   📈 Session Statistics and Metrics"
echo "   🔄 Interactive Chat Interface"
echo ""
echo "📋 Next Steps:"
echo "   1. Test the chatbot interface by visiting $FRONTEND_URL/chatbot"
echo "   2. Verify MCP flow steps are displayed during chat"
echo "   3. Check that policies are being applied to responses"
echo "   4. Configure Azure OpenAI API keys for production use"
echo ""
echo "✅ Chatbot interface ready for use!"
