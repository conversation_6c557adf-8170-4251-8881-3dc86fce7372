#!/bin/bash
set -e

echo "🗄️ Updating Database Schema for Enhanced Features..."
echo "=================================================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "Backing up existing database"
export PGPASSWORD="$DB_PASSWORD"

# Create backup
BACKUP_FILE="../backups/vitea_db_backup_$(date +%Y%m%d_%H%M%S).sql"
mkdir -p ../backups

pg_dump -h "${DB_HOST}" \
        -p 5432 \
        -d vitea_db \
        -U dbadmin \
        --no-password \
        > "$BACKUP_FILE"

check_success "Database backup created: $BACKUP_FILE"

log_step "Applying enhanced database schema"
psql -h "${DB_HOST}" \
     -p 5432 \
     -d vitea_db \
     -U dbadmin \
     -f ../configs/enhanced-database-schema.sql \
     --set sslmode=require

check_success "Enhanced schema applied"

log_step "Applying Rego Blob Columns"
psql -h "${DB_HOST}" \
     -p 5432 \
     -d vitea_db \
     -U dbadmin \
     -f ../configs/20250716_01__add_rego_blob_columns.sql \
     --set sslmode=require

check_success "Rego Blob Columns applied"

log_step "Applying Create Agents Rego Templates"
psql -h "${DB_HOST}" \
     -p 5432 \
     -d vitea_db \
     -U dbadmin \
     -f ../configs/20250716_02__create_agents_rego_templates.sql \
     --set sslmode=require

check_success "Create Agents Rego Templates applied"

log_step "Inserting Default Agent Rego Templates"
psql -h "${DB_HOST}" \
     -p 5432 \
     -d vitea_db \
     -U dbadmin \
     -f ../configs/20250716_03__insert_default_agent_rego_templates.sql \
     --set sslmode=require

check_success "Inserting Default Agent Rego Templates applied"

log_step "Applying Audit Log Enhancements"
psql -h "${DB_HOST}" \
     -p 5432 \
     -d vitea_db \
     -U dbadmin \
     -f ../configs/20250716_04__audit_log_enhancements.sql \
     --set sslmode=require

check_success "Audit Log Enhancements applied"

log_step "Applying Rego Generation Functions"
psql -h "${DB_HOST}" \
     -p 5432 \
     -d vitea_db \
     -U dbadmin \
     -f ../configs/20250716_05__rego_generation_functions.sql \
     --set sslmode=require

check_success "Rego Generation Functions applied"

log_step "Verifying new tables"
NEW_TABLES=$(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST}" \
    -p 5432 -d vitea_db -U dbadmin \
    -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('mcp_chat_sessions', 'mcp_flow_steps', 'chat_messages', 'policy_executions', 'openai_api_calls');" \
    --set sslmode=require | xargs)

if [ "$NEW_TABLES" -eq 5 ]; then
    echo "✅ All new tables created successfully"
else
    echo "❌ Expected 5 new tables, found $NEW_TABLES"
    exit 1
fi

echo ""
echo "🎉 Database schema update completed!"
echo "New tables: mcp_chat_sessions, mcp_flow_steps, chat_messages, policy_executions, openai_api_calls"