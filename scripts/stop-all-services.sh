#!/bin/bash

# Script to stop all Vitea services across separate repositories
# Stops services in reverse order to handle dependencies gracefully

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# All services in the system (reverse order for graceful shutdown)
ALL_SERVICES=(
    # Supporting services first (least dependent)
    "../vitea-mcp-server:MCP Server"
    "../envoy_fhir_proxy:Envoy FHIR Proxy"
    
    # Core services (in reverse dependency order)
    ".:Pilot Services"
    "../PiiGuard:PII Guard"
    "../policy_runtime:Policy Runtime" 
    "../testing-observability-platform:Testing & Evaluation Platform"
)

echo "🛑 Vitea Distributed Service Stopper"
echo "===================================="
echo "Stopping services in reverse dependency order for graceful shutdown"
echo ""

# Function to stop a single service
stop_service() {
    local repo_path=$1
    local service_name=$2
    local stopped_any=1
    
    if [ ! -d "$repo_path" ]; then
        warn "Repository not found: $repo_path (skipping)"
        return 0
    fi
    
    log "Checking for running services in $repo_path ($service_name)..."
    
    cd "$repo_path"
    
    # Find the appropriate docker-compose file
    # Special handling for Policy Runtime which uses docker-compose.full.yml
    local compose_file=""
    if [[ "$service_name" == *"Policy Runtime"* ]] && [ -f "docker-compose.full.yml" ]; then
        compose_file="docker-compose.full.yml"
        log "Using docker-compose.full.yml for Policy Runtime"
    elif [ -f "docker-compose.yml" ]; then
        compose_file="docker-compose.yml"
    elif [ -f "docker-compose.yaml" ]; then
        compose_file="docker-compose.yaml"
    else
        warn "No docker-compose file found in $repo_path"
        cd - > /dev/null
        return 0
    fi
    
    # Check if there are any running containers
    local running_containers=$(docker-compose -f "$compose_file" ps -q 2>/dev/null || true)
    
    if [ -n "$running_containers" ]; then
        stopped_any=0
        log "Stopping Docker containers for $service_name..."
        
        # First try graceful shutdown
        docker-compose -f "$compose_file" stop 2>/dev/null || {
            warn "Graceful stop failed for $service_name, trying force stop..."
        }
        
        # Then remove containers, networks, volumes
        docker-compose -f "$compose_file" down -v 2>/dev/null || {
            warn "Some containers may have already been stopped manually"
        }
        
        success "$service_name containers stopped"
    else
        log "No running containers found for $service_name"
    fi
    
    # Return to original directory
    cd - > /dev/null
    
    return $stopped_any
}

log "Stopping all Vitea services in reverse dependency order..."
echo ""

# Stop services in reverse order
services_found=false
for repo_info in "${ALL_SERVICES[@]}"; do
    IFS=':' read -r repo_path service_name <<< "$repo_info"
    
    if stop_service "$repo_path" "$service_name"; then
        services_found=true
    fi
    
    # Small delay between service stops
    sleep 2
done

echo ""
log "=== Cleanup Phase: Checking for orphaned containers ==="

# Look for containers with vitea-related names or common patterns
orphaned_containers=$(docker ps --format "table {{.Names}}\t{{.Image}}" | grep -i -E "(vitea|pilot|mcp|fhir|envoy|piiguard|policy.*runtime|evaluator|testing.*observability)" | grep -v NAMES || true)

if [ -n "$orphaned_containers" ]; then
    warn "Found potentially orphaned Vitea containers:"
    echo "$orphaned_containers"
    echo ""
    read -p "Stop these containers too? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Extract container names and stop them
        container_names=$(echo "$orphaned_containers" | awk '{print $1}' | tail -n +2)
        if [ -n "$container_names" ]; then
            log "Stopping orphaned containers..."
            echo "$container_names" | xargs docker stop 2>/dev/null || true
            echo "$container_names" | xargs docker rm 2>/dev/null || true
            success "Orphaned containers stopped"
            services_found=true
        fi
    fi
fi

# Clean up Docker networks that might be left behind
log "Cleaning up Docker networks..."
vitea_networks=$(docker network ls --format "{{.Name}}" | grep -i -E "(vitea|pilot|mcp|fhir|envoy|piiguard|policy|evaluator)" || true)
if [ -n "$vitea_networks" ]; then
    log "Found Vitea-related networks, attempting cleanup..."
    echo "$vitea_networks" | xargs -r docker network rm 2>/dev/null || true
fi

# Clean up any temporary files from startup script
log "Cleaning up temporary files..."
rm -f /tmp/vitea-shutdown /tmp/vitea-services.pid /tmp/vitea-*-startup.log

# Final status check
echo ""
if [ "$services_found" = true ]; then
    success "🎉 All Vitea services have been stopped!"
    echo ""
    log "=== Shutdown Summary ==="
    log "Services stopped in order:"
    log "  1. Supporting services (Testing, MCP, Envoy)"
    log "  2. Core application (Pilot)"
    log "  3. Security services (PII Guard)"  
    log "  4. Policy services (Policy Runtime)"
    log "  5. Foundation services (Evaluators)"
    echo ""
else
    log "ℹ️  No running Vitea services were found"
fi

echo ""
log "=== Next Steps ==="
log "  • Start services again with: ./start-all-services.sh"
log "  • Check for any remaining containers: docker ps"
log "  • Check for any remaining networks: docker network ls"
log "  • Clean up all unused resources: docker system prune"
echo ""

success "✅ Distributed system shutdown complete!"