#!/bin/bash
set -e

echo "🗄️ Setting up PostgreSQL Database..."
echo "===================================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "Generating secure database password"
export DB_PASSWORD=$(openssl rand -base64 32)
echo "Password generated (32 characters)" 

log_step "Storing password in Key Vault"
az keyvault secret set \
  --vault-name "${COMPANY_NAME}-${ENVIRONMENT}-kv" \
  --name "db-admin-password" \
  --value "$DB_PASSWORD" \
  --only-show-errors
check_success "Password stored in Key Vault"

log_step "Creating PostgreSQL flexible-server (this may take 5-10 minutes)"
echo "⏱️ Creating server: ${COMPANY_NAME}-${ENVIRONMENT}-postgres"
az postgres flexible-server create \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-postgres" \
  --location $LOCATION \
  --admin-user dbadmin \
  --admin-password "$DB_PASSWORD" \
  --sku-name Standard_B1ms \
  --tier Burstable \
  --storage-size 32
check_success "PostgreSQL server created"

log_step "Configuring firewall for Azure services"
az postgres flexible-server firewall-rule create \
  --resource-group $RESOURCE_GROUP \
  --name "${COMPANY_NAME}-${ENVIRONMENT}-postgres" \
  --rule-name "AllowAzureServices" \
  --start-ip-address 0.0.0.0 \
  --end-ip-address 0.0.0.0 \
  --only-show-errors
check_success "Firewall configured"

log_step "Creating Vitea database"
az postgres flexible-server db create \
  --resource-group $RESOURCE_GROUP \
  --server-name "${COMPANY_NAME}-${ENVIRONMENT}-postgres" \
  --database-name vitea_db \
  --only-show-errors
check_success "Database created"

# Save connection details
echo "export DB_PASSWORD=\"$DB_PASSWORD\"" >> ../configs/environment.conf
echo "export DB_HOST=\"${COMPANY_NAME}-${ENVIRONMENT}-postgres.postgres.database.azure.com\"" >> ../configs/environment.conf

echo ""
echo "🎉 Database setup completed successfully!"
echo "Database server: ${COMPANY_NAME}-${ENVIRONMENT}-postgres.postgres.database.azure.com"
echo "Next: Run script 3 (Deploy schema)"
