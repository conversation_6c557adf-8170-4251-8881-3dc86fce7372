#!/bin/bash
set -e

# Color codes for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Enhanced logging functions
log_info() {
    echo -e "${BLUE}ℹ️  INFO: $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ SUCCESS: $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  WARNING: $1${NC}"
}

log_error() {
    echo -e "${RED}❌ ERROR: $1${NC}"
}

log_step() {
    echo ""
    echo -e "${BLUE}📋 STEP: $1${NC}"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        log_success "$1"
    else
        log_error "$1"
        exit 1
    fi
}

# Load environment variables
if [ -f "../configs/environment.conf" ]; then
    source ../configs/environment.conf
    log_info "Environment variables loaded"
else
    log_error "Environment configuration not found!"
    exit 1
fi

# Create timestamped log
LOG_FILE="../logs/$(basename $0)_$(date +%Y%m%d_%H%M%S).log"
mkdir -p ../logs
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

log_info "Script started: $(date)"
log_info "Log file: $LOG_FILE"
