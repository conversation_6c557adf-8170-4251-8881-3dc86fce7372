#!/bin/bash

# =============================================================================
# Safe Database Recreation and Demo Data Loading Script (v2)
# =============================================================================
# This script safely recreates the database and loads demo data in the correct sequence
# 
# Usage:
#   ./scripts/load-demo-data-v2.sh
#   
# Environment Variables (loads from .env file):
#   DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if PostgreSQL client is available
check_psql() {
    if ! command -v psql &> /dev/null; then
        log_error "psql command not found. Please install PostgreSQL client."
        exit 1
    fi
}

# Function to load environment variables from .env file
load_env_file() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local env_file="$script_dir/../.env"
    
    if [ -f "$env_file" ]; then
        log_info "Loading environment variables from .env file"
        # Source the file directly to handle it properly
        set -a  # automatically export all variables
        source "$env_file"
        set +a  # stop auto-exporting
    else
        log_error ".env file not found at $env_file"
        exit 1
    fi
}

# Function to execute PostgreSQL commands
execute_psql() {
    local sql_content="$1"
    local description="$2"
    
    if [ -n "$description" ]; then
        log_info "$description"
    fi
    
    # Use environment variables directly with PGPASSWORD
    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "$sql_content" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to execute PostgreSQL from file
execute_psql_file() {
    local sql_file="$1"
    local description="$2"
    
    if [ -n "$description" ]; then
        log_info "$description"
    fi
    
    if [ ! -f "$sql_file" ]; then
        log_error "SQL file not found: $sql_file"
        return 1
    fi
    
    # Use environment variables directly with PGPASSWORD
    if PGPASSWORD="$DB_PASSWORD" psql -h localhost -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$sql_file"; then
        return 0
    else
        return 1
    fi
}

# Function to test database connection
test_connection() {
    log_info "Testing database connection..."
    log_info "Host: localhost, Port: $DB_PORT, Database: $DB_NAME, User: $DB_USER"
    
    if execute_psql "SELECT version();" > /dev/null 2>&1; then
        log_success "Database connection successful"
        return 0
    else
        log_error "Cannot connect to database. Please check:"
        log_error "  1. Docker services are running (docker-compose up)"
        log_error "  2. Database credentials in .env file"
        log_error "  3. Database port $DB_PORT is accessible"
        return 1
    fi
}

# Function to backup existing data (optional)
backup_database() {
    local backup_file="vitea_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    log_info "Creating backup: $backup_file"
    
    if PGPASSWORD="$DB_PASSWORD" pg_dump -h localhost -p "$DB_PORT" -U "$DB_USER" "$DB_NAME" > "$backup_file" 2>/dev/null; then
        log_success "Backup created: $backup_file"
        return 0
    else
        log_warning "Backup failed, but continuing..."
        return 1
    fi
}

# Function to drop all tables safely
drop_all_tables() {
    log_info "Dropping all existing tables..."
    
    PGPASSWORD="$DB_PASSWORD" psql -h localhost -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'EOF'
-- Disable triggers to avoid dependency issues
SET session_replication_role = replica;

-- Drop all tables in correct order to handle dependencies
DROP TABLE IF EXISTS agent_role_policies CASCADE;
DROP TABLE IF EXISTS integration_dlq CASCADE;
DROP TABLE IF EXISTS integration_outbox CASCADE;
DROP TABLE IF EXISTS guardrail_services CASCADE;
DROP TABLE IF EXISTS alembic_version CASCADE;
DROP TABLE IF EXISTS enum_values CASCADE;
DROP TABLE IF EXISTS enum_categories CASCADE;
DROP TABLE IF EXISTS agent_policies CASCADE;
DROP TABLE IF EXISTS agent_access CASCADE;
DROP TABLE IF EXISTS policy_group_policies CASCADE;
DROP TABLE IF EXISTS policy_groups CASCADE;
DROP TABLE IF EXISTS user_roles CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS openai_api_calls CASCADE;
DROP TABLE IF EXISTS policy_executions CASCADE;
DROP TABLE IF EXISTS chat_messages CASCADE;
DROP TABLE IF EXISTS mcp_flow_steps CASCADE;
DROP TABLE IF EXISTS mcp_chat_sessions CASCADE;
DROP TABLE IF EXISTS system_metrics CASCADE;
DROP TABLE IF EXISTS policy_templates CASCADE;
DROP TABLE IF EXISTS rego_templates CASCADE;
DROP TABLE IF EXISTS agents CASCADE;
DROP TABLE IF EXISTS audit_log CASCADE;
DROP TABLE IF EXISTS documents CASCADE;
DROP TABLE IF EXISTS policy_violations CASCADE;
DROP TABLE IF EXISTS policies CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop custom types
DROP TYPE IF EXISTS agent_status_enum CASCADE;
DROP TYPE IF EXISTS user_status_enum CASCADE;
DROP TYPE IF EXISTS link_type_enum CASCADE;
DROP TYPE IF EXISTS lifecycle_status_enum CASCADE;
DROP TYPE IF EXISTS severity_level_enum CASCADE;
DROP TYPE IF EXISTS access_level_enum CASCADE;

-- Drop functions
DROP FUNCTION IF EXISTS update_updated_at_column CASCADE;
DROP FUNCTION IF EXISTS log_rego_operation(UUID, JSONB) CASCADE;
DROP FUNCTION IF EXISTS generate_rego_for_policy CASCADE;
DROP FUNCTION IF EXISTS rollback_rego_generation CASCADE;
DROP FUNCTION IF EXISTS log_hipaa_audit_event CASCADE;
DROP FUNCTION IF EXISTS get_policy_template_by_category CASCADE;
DROP FUNCTION IF EXISTS clone_policy CASCADE;
DROP FUNCTION IF EXISTS search_policies(VARCHAR, VARCHAR, VARCHAR, BOOLEAN, INTEGER, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS get_enum_values(VARCHAR, VARCHAR) CASCADE;
DROP FUNCTION IF EXISTS get_enum_fields_for_policy_type CASCADE;
DROP FUNCTION IF EXISTS integration_enqueue_event CASCADE;
DROP FUNCTION IF EXISTS update_enum_categories_updated_at CASCADE;
DROP FUNCTION IF EXISTS update_enum_values_updated_at CASCADE;
DROP FUNCTION IF EXISTS trg_agents_enqueue CASCADE;
DROP FUNCTION IF EXISTS trg_roles_enqueue CASCADE;
DROP FUNCTION IF EXISTS trg_policies_enqueue CASCADE;
DROP FUNCTION IF EXISTS trg_arp_enqueue CASCADE;

-- Re-enable triggers
SET session_replication_role = DEFAULT;
EOF

    if [ $? -eq 0 ]; then
        log_success "All tables and functions dropped"
    else
        log_error "Failed to drop tables"
        exit 1
    fi
}

# Function to create schema
create_schema() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local schema_file="$script_dir/00-create-complete-postgres-schema-consolidated.sql"
    
    if [ ! -f "$schema_file" ]; then
        log_error "Schema file not found: $schema_file"
        exit 1
    fi
    
    log_info "Creating database schema..."
    
    if execute_psql_file "$schema_file"; then
        log_success "Database schema created successfully"
    else
        log_error "Failed to create database schema"
        exit 1
    fi
}

# Function to load corrected demo data
load_demo_data() {
    log_info "Loading HIPAA demo data..."
    
    # Create corrected demo data with proper sequencing and fixes
    PGPASSWORD="$DB_PASSWORD" psql -h localhost -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'EOF'
-- =============================================================================
-- CORRECTED HIPAA Sample Data for Development Environment
-- =============================================================================

BEGIN;

-- First, add the missing granted_by column to agent_access table
ALTER TABLE agent_access ADD COLUMN IF NOT EXISTS granted_by UUID REFERENCES users(user_id);

-- =============================================================================
-- 1. USERS TABLE (5 healthcare staff) - MUST BE FIRST
-- =============================================================================

-- Store user IDs in variables for foreign key references
\set admin_user_id 'a1b2c3d4-e5f6-7890-abcd-111111111111'
\set sarah_user_id 'a1b2c3d4-e5f6-7890-abcd-************'
\set jennifer_user_id 'a1b2c3d4-e5f6-7890-abcd-************'
\set michael_user_id 'a1b2c3d4-e5f6-7890-abcd-************'
\set lisa_user_id 'a1b2c3d4-e5f6-7890-abcd-************'

INSERT INTO users (
    user_id, azure_ad_id, email, first_name, last_name, role, is_active, 
    department, status, risk_score, two_factor_enabled, created_at, updated_at
) VALUES 
-- Admin User (System Administrator)
(:'admin_user_id', 'azure-admin-placeholder-001', '<EMAIL>', 
 'HIPAA', 'Administrator', 'admin', true, 'Information Technology', 'active', 0.0, true, NOW(), NOW()),

-- Dr. Sarah Martinez (Medical Director)  
(:'sarah_user_id', 'azure-sarah-martinez-002', '<EMAIL>',
 'Sarah', 'Martinez', 'user', true, 'Medical Affairs', 'active', 1.5, true, NOW(), NOW()),

-- Jennifer Chen (Compliance Officer)
(:'jennifer_user_id', 'azure-jennifer-chen-003', '<EMAIL>',
 'Jennifer', 'Chen', 'user', true, 'Privacy & Compliance', 'active', 0.5, true, NOW(), NOW()),

-- Michael Rodriguez (Clinical Reviewer)
(:'michael_user_id', 'azure-michael-rodriguez-004', '<EMAIL>',
 'Michael', 'Rodriguez', 'user', true, 'Clinical Review', 'active', 1.0, false, NOW(), NOW()),

-- Lisa Thompson (Case Manager)
(:'lisa_user_id', 'azure-lisa-thompson-005', '<EMAIL>',
 'Lisa', 'Thompson', 'user', true, 'Care Management', 'active', 0.8, true, NOW(), NOW())
 ON CONFLICT (azure_ad_id) DO NOTHING;

-- =============================================================================
-- 2. ROLES TABLE (5 HIPAA-focused roles)
-- =============================================================================

\set compliance_role_id 'b1b2c3d4-e5f6-7890-abcd-111111111111'
\set clinical_role_id 'b1b2c3d4-e5f6-7890-abcd-************'
\set medical_dir_role_id 'b1b2c3d4-e5f6-7890-abcd-************'
\set case_mgr_role_id 'b1b2c3d4-e5f6-7890-abcd-************'
\set hipaa_admin_role_id 'b1b2c3d4-e5f6-7890-abcd-************'

INSERT INTO roles (role_id, code, name, description, created_at) VALUES
(:'compliance_role_id', 'HIPAA_COMPLIANCE_OFFICER', 'HIPAA Privacy and Compliance Officer', 
 'Responsible for HIPAA compliance monitoring and privacy oversight', NOW()),

(:'clinical_role_id', 'HIPAA_CLINICAL_REVIEWER', 'Clinical Staff with PHI Access',
 'Healthcare staff authorized to review patient medical information', NOW()),

(:'medical_dir_role_id', 'HIPAA_MEDICAL_DIRECTOR', 'Senior Physician with Full Access',
 'Senior medical staff with unrestricted access to patient data', NOW()),

(:'case_mgr_role_id', 'HIPAA_CASE_MANAGER', 'Patient Care Coordinator',
 'Staff responsible for coordinating patient care across services', NOW()),

(:'hipaa_admin_role_id', 'HIPAA_ADMIN', 'System Administrator',
 'Technical staff with system administration privileges', NOW())
 ON CONFLICT (code) DO NOTHING;

-- =============================================================================
-- 3. USER_ROLES TABLE (User-role assignments)
-- =============================================================================

INSERT INTO user_roles (user_id, role_id) VALUES
-- Admin User
(:'admin_user_id', :'hipaa_admin_role_id'),

-- Dr. Sarah Martinez (Medical Director + Clinical Reviewer)
(:'sarah_user_id', :'medical_dir_role_id'),
(:'sarah_user_id', :'clinical_role_id'),

-- Jennifer Chen (Compliance Officer)
(:'jennifer_user_id', :'compliance_role_id'),

-- Michael Rodriguez (Clinical Reviewer)
(:'michael_user_id', :'clinical_role_id'),

-- Lisa Thompson (Case Manager)
(:'lisa_user_id', :'case_mgr_role_id')
ON CONFLICT (user_id, role_id) DO NOTHING;

-- =============================================================================
-- 4. AGENTS TABLE (1 HIPAA compliance agent)
-- =============================================================================

\set hipaa_agent_id 'c1b2c3d4-e5f6-7890-abcd-111111111111'

INSERT INTO agents (
    agent_id, name, description, agent_type, is_active, 
    configuration, vendor, department, risk_score, status, created_at, updated_at, created_by
) VALUES (
    :'hipaa_agent_id',
    'BCBS HIPAA Compliance Agent',
    'AI agent specialized in HIPAA compliance monitoring and PHI protection',
    'healthcare_hipaa_compliance',
    true,
    '{
        "redaction_enabled": true,
        "audit_all_access": true,
        "emergency_override": true,
        "supported_redaction_types": ["ssn", "phone", "address", "email", "dob", "insurance_id"],
        "compliance_version": "HIPAA_2023",
        "max_session_duration": 3600,
        "require_two_factor": false,
        "alert_on_violations": true,
        "auto_log_access": true
    }'::jsonb,
    'BCBS Blue Cross',
    'Privacy & Compliance',
    0.2,
    'active',
    NOW(),
    NOW(),
    :'admin_user_id'
)
ON CONFLICT (name) DO NOTHING;

-- =============================================================================
-- 5. POLICY_GROUPS TABLE (1 HIPAA compliance group)
-- =============================================================================

\set hipaa_group_id 'd1b2c3d4-e5f6-7890-abcd-111111111111'

INSERT INTO policy_groups (
    group_id, name, description, status, created_at, updated_at, created_by
) VALUES (
    :'hipaa_group_id',
    'HIPAA Compliance Policy Suite',
    'Comprehensive HIPAA privacy and security policies for healthcare operations',
    'active',
    NOW(),
    NOW(),
    :'admin_user_id'
)
ON CONFLICT (name) DO NOTHING;

-- =============================================================================
-- 6. POLICY_TEMPLATES TABLE (3 HIPAA templates)
-- =============================================================================

\set template_1_id 'e1b2c3d4-e5f6-7890-abcd-111111111111'
\set template_2_id 'e1b2c3d4-e5f6-7890-abcd-************'
\set template_3_id 'e1b2c3d4-e5f6-7890-abcd-************'

INSERT INTO policy_templates (
    template_id, category, name, description, template_definition, 
    created_at, created_by
) VALUES 
-- Template 1: Medical Record Sharing with Phone Redaction Template
(:'template_1_id', 'HIPAA Privacy Compliance', 'HIPAA PHI Access Control Template',
 'Template for controlling access to Protected Health Information',
 '{
     "parameters": {
         "sharing_authorized_roles": {"type": "array", "default": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR"]},
         "redaction_required": {"type": "boolean", "default": true},
         "redaction_fields": {"type": "array", "default": ["phone"]},
         "redaction_rules": {"type": "object", "default": {"phone": "({area_code}) ***-****"}},
         "emergency_override": {"type": "boolean", "default": true},
         "audit_redaction": {"type": "boolean", "default": true},
         "external_sharing_allowed": {"type": "boolean", "default": false}
     }
 }'::jsonb,
 NOW(), :'admin_user_id'),

-- Template 2: Minimum Necessary Standard with Email Redaction Template
(:'template_2_id', 'HIPAA Privacy Compliance', 'HIPAA Medical Record Sharing Template',
 'Template for sharing medical records with PHI redaction capabilities',
 '{
     "parameters": {
         "auto_redaction_enabled": {"type": "boolean", "default": true},
         "pii_fields_to_redact": {"type": "array", "default": ["email"]},
         "role_based_visibility": {"type": "boolean", "default": true},
         "visibility_rules": {"type": "object", "default": {
             "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email"],
             "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email"],
             "HIPAA_MEDICAL_DIRECTOR": ["all_fields"],
             "HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email"]
         }},
         "redaction_methods": {"type": "object", "default": {"email": "****@****.com"}},
         "minimum_data_only": {"type": "boolean", "default": true},
         "purpose_limitation": {"type": "boolean", "default": true}
     }  
 }'::jsonb,
 NOW(), :'admin_user_id'),

-- Template 3: Minimum Necessary Standard with Auto-Redaction Template
(:'template_3_id', 'HIPAA Privacy Compliance', 'HIPAA Patient Consent Management Template',
 'Template for managing patient consent and authorization',
 '{
     "parameters": {
         "auto_redaction_enabled": {"type": "boolean", "default": true},
         "pii_fields_to_redact": {"type": "array", "default": ["ssn", "address", "phone", "email", "dob", "insurance_id"]},
         "role_based_visibility": {"type": "boolean", "default": true},
         "visibility_rules": {"type": "object", "default": {
             "HIPAA_CLINICAL_REVIEWER": ["medical_info", "basic_demographics", "email", "phone", "address", "dob", "insurance_id"],
             "HIPAA_COMPLIANCE_OFFICER": ["access_logs", "audit_trails", "email", "phone", "address", "dob", "insurance_id"],
             "HIPAA_MEDICAL_DIRECTOR": ["all_fields"],
             "HIPAA_CASE_MANAGER": ["contact_info", "insurance_info", "email", "phone", "address", "dob", "insurance_id"]
         }},
         "redaction_methods": {"type": "object", "default": {
             "ssn": "***-**-{last_4}",
             "phone": "({area_code}) ***-****",
             "address": "{city}, {state} {zip}",
             "email": "****@****.com",
             "dob": "{month}/{day}/****",
             "insurance_id": "{first_3}*****"
         }},
         "minimum_data_only": {"type": "boolean", "default": true},
         "purpose_limitation": {"type": "boolean", "default": true}
     }
 }'::jsonb,
 NOW(), :'admin_user_id')
 ON CONFLICT (template_id) DO NOTHING;

-- =============================================================================
-- 7. POLICIES TABLE (3 actual HIPAA policies)
-- =============================================================================

\set policy_1_id 'f1b2c3d4-e5f6-7890-abcd-111111111111'
\set policy_2_id 'f1b2c3d4-e5f6-7890-abcd-************'
\set policy_3_id 'f1b2c3d4-e5f6-7890-abcd-************'

INSERT INTO policies (
    policy_id, name, description, category, policy_type, definition, version, 
    is_active, severity, applies_to_roles, created_at, updated_at, created_by
) VALUES 
-- Policy 1: Medical Record Sharing with Phone Redaction
(:'policy_1_id', 'BCBS Minimum Necessary with Phone Redaction',
 'Secure sharing of medical records with automatic PHI redaction',
 'HIPAA Privacy Compliance', 'opa',
 '{
     "sharing_authorized_roles": ["HIPAA_CLINICAL_REVIEWER", "HIPAA_MEDICAL_DIRECTOR", "HIPAA_COMPLIANCE_OFFICER"],
     "redaction_required": true,
     "redaction_fields": ["phone"],
     "redaction_rules": {
         "phone": "({area_code}) ***-****"
     },
     "emergency_override": true,
     "audit_redaction": true,
     "external_sharing_allowed": false
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_COMPLIANCE_OFFICER'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 2: Minimum Necessary Standard with Email Redaction
(:'policy_2_id', 'BCBS Minimum Necessary with Email Redaction',
 'Ensures only minimum necessary PHI is disclosed based on user role',
 'HIPAA Privacy Compliance', 'opa',
 '{
     "auto_redaction_enabled": true,
     "pii_fields_to_redact": ["email"],
     "role_based_visibility": true,
     "visibility_rules": {
         "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"],
         "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"],
         "MEDICAL_DIRECTOR": ["all_fields"],
         "CASE_MANAGER": ["contact_info", "insurance_info"]
     },
     "redaction_methods": {
         "email": "****@****.com"
     },
     "minimum_data_only": true,
     "purpose_limitation": true
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_CASE_MANAGER'],
 NOW(), NOW(), :'admin_user_id'),

-- Policy 3: Minimum Necessary Standard with Auto-Redaction
(:'policy_3_id', 'BCBS Minimum Necessary with Auto-Redaction',
 'Ensures only minimum necessary PHI is disclosed based on user role',
 'HIPAA Privacy Compliance', 'opa',
 '{
     "auto_redaction_enabled": true,
     "pii_fields_to_redact": ["ssn", "address", "phone", "email", "dob", "insurance_id"],
     "role_based_visibility": true,
     "visibility_rules": {
         "CLINICAL_REVIEWER": ["medical_info", "basic_demographics"],
         "COMPLIANCE_OFFICER": ["access_logs", "audit_trails"],
         "MEDICAL_DIRECTOR": ["all_fields"],
         "CASE_MANAGER": ["contact_info", "insurance_info"]
     },
     "redaction_methods": {
         "ssn": "***-**-{last_4}",
         "phone": "({area_code}) ***-****",
         "address": "{city}, {state} {zip}",
         "email": "****@****.com",
         "dob": "{month}/{day}/****",
         "insurance_id": "{first_3}*****"
     },
     "minimum_data_only": true,
     "purpose_limitation": true
 }'::jsonb,
 1, true, 'high',
 ARRAY['HIPAA_CLINICAL_REVIEWER', 'HIPAA_MEDICAL_DIRECTOR', 'HIPAA_CASE_MANAGER'],
 NOW(), NOW(), :'admin_user_id')
 ON CONFLICT (name, version) DO NOTHING;

-- =============================================================================
-- 8. POLICY_GROUP_POLICIES TABLE (Link policies to the HIPAA group)
-- =============================================================================

INSERT INTO policy_group_policies (group_id, policy_id) VALUES
(:'hipaa_group_id', :'policy_1_id'),
(:'hipaa_group_id', :'policy_2_id'),
(:'hipaa_group_id', :'policy_3_id')
ON CONFLICT (group_id, policy_id) DO NOTHING;

-- =============================================================================
-- 9. AGENT_POLICIES TABLE (Link agent to policies)
-- =============================================================================

INSERT INTO agent_policies (agent_id, policy_id, link_type) VALUES
(:'hipaa_agent_id', :'policy_1_id', 'via_group'),
(:'hipaa_agent_id', :'policy_2_id', 'via_group'),
(:'hipaa_agent_id', :'policy_3_id', 'via_group')
ON CONFLICT (agent_id, policy_id) DO NOTHING;

-- =============================================================================
-- 10. AGENT_ACCESS TABLE (Role-based access to the agent)
-- =============================================================================

INSERT INTO agent_access (agent_id, role_id, access_level, granted_by) VALUES
(:'hipaa_agent_id', :'compliance_role_id', 'manage', :'admin_user_id'),
(:'hipaa_agent_id', :'clinical_role_id', 'view', :'admin_user_id'),
(:'hipaa_agent_id', :'medical_dir_role_id', 'manage', :'admin_user_id'),
(:'hipaa_agent_id', :'case_mgr_role_id', 'view', :'admin_user_id'),
(:'hipaa_agent_id', :'hipaa_admin_role_id', 'manage', :'admin_user_id')
ON CONFLICT (agent_id, role_id) DO NOTHING;

-- =============================================================================
-- 11. SAMPLE AUDIT_LOG ENTRIES (10 realistic entries)
-- =============================================================================

INSERT INTO audit_log (
    user_id, action, resource_type, resource_id, new_values,
    ip_address, user_agent, timestamp
) VALUES 
-- Entry 1: PHI Access by Clinical Reviewer
(:'michael_user_id', 'HIPAA_PHI_ACCESS', 'patient_record', 
 uuid_generate_v4(),
 '{"access_type": "read", "redaction_applied": true, "fields_accessed": ["demographics", "medical_history"]}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '2 hours'),

-- Entry 2: Policy Execution by Compliance Officer
(:'jennifer_user_id', 'HIPAA_POLICY_EXECUTION', 'policy', :'policy_1_id',
 '{"execution_result": "allow", "redaction_count": 3, "compliance_score": 95}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '1 hour'),

-- Entry 3: Medical Record Sharing with Redaction
(:'sarah_user_id', 'HIPAA_RECORD_SHARING', 'medical_record',
 uuid_generate_v4(),
 '{"sharing_method": "secure_portal", "recipient": "external_specialist", "redaction_applied": true}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
 NOW() - INTERVAL '3 hours'),

-- Entry 4: Patient Consent Update
(:'lisa_user_id', 'HIPAA_CONSENT_UPDATE', 'patient_consent',
 uuid_generate_v4(),
 '{"consent_status": "granted", "consent_types": ["treatment", "payment", "operations"]}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
 NOW() - INTERVAL '4 hours'),

-- Entry 5: Failed Login Attempt
(:'michael_user_id', 'HIPAA_FAILED_LOGIN', 'user_session',
 NULL,
 '{"failure_reason": "invalid_password", "attempt_count": 2, "source_ip": "*************"}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
 NOW() - INTERVAL '5 hours'),

-- Entry 6: Audit Report Generation
(:'jennifer_user_id', 'HIPAA_AUDIT_REPORT', 'system_report',
 uuid_generate_v4(),
 '{"report_type": "monthly_access", "records_count": 1247, "violations_found": 0}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
 NOW() - INTERVAL '6 hours'),

-- Entry 7: Emergency Override Used
(:'sarah_user_id', 'HIPAA_EMERGENCY_OVERRIDE', 'policy_execution',
 :'policy_2_id',
 '{"redaction_enabled": false, "override_reason": "medical_emergency", "patient_id": "12345"}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
 NOW() - INTERVAL '8 hours'),

-- Entry 8: Third-Party Data Request
(:'jennifer_user_id', 'HIPAA_THIRD_PARTY_REQUEST', 'data_sharing',
 uuid_generate_v4(),
 '{"requesting_entity": "Mayo Clinic", "baa_status": "verified", "data_types": ["lab_results", "imaging"]}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
 NOW() - INTERVAL '12 hours'),

-- Entry 9: Patient Access Request
(:'lisa_user_id', 'HIPAA_PATIENT_ACCESS_REQUEST', 'patient_request',
 uuid_generate_v4(),
 '{"request_type": "medical_records", "delivery_method": "electronic", "patient_verified": true}'::jsonb,
 '*************'::inet,
 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
 NOW() - INTERVAL '1 day'),

-- Entry 10: System Configuration Change
(:'admin_user_id', 'HIPAA_CONFIG_CHANGE', 'system_config',
 uuid_generate_v4(),
 '{"redaction_timeout": 3600}'::jsonb,
 '************'::inet,
 'Mozilla/5.0 (Ubuntu; Linux x86_64) AppleWebKit/537.36',
 NOW() - INTERVAL '2 days');

-- =============================================================================
-- SUCCESS MESSAGE
-- =============================================================================

-- Display summary of inserted data
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🏥 HIPAA Sample Data Initialization Complete!';
    RAISE NOTICE '================================================';
    RAISE NOTICE 'Successfully inserted:';
    RAISE NOTICE '  • % Users', (SELECT COUNT(*) FROM users);
    RAISE NOTICE '  • % Roles', (SELECT COUNT(*) FROM roles);
    RAISE NOTICE '  • % User-Role assignments', (SELECT COUNT(*) FROM user_roles);
    RAISE NOTICE '  • % Agents', (SELECT COUNT(*) FROM agents);
    RAISE NOTICE '  • % Policy Templates', (SELECT COUNT(*) FROM policy_templates);
    RAISE NOTICE '  • % Policies', (SELECT COUNT(*) FROM policies);
    RAISE NOTICE '  • % Policy Groups', (SELECT COUNT(*) FROM policy_groups);
    RAISE NOTICE '  • % Policy-Group links', (SELECT COUNT(*) FROM policy_group_policies);
    RAISE NOTICE '  • % Agent-Policy links', (SELECT COUNT(*) FROM agent_policies);
    RAISE NOTICE '  • % Agent Access entries', (SELECT COUNT(*) FROM agent_access);
    RAISE NOTICE '  • % Audit Log entries', (SELECT COUNT(*) FROM audit_log);
    RAISE NOTICE '';
    RAISE NOTICE 'Database is ready for HIPAA compliance demos!';
    RAISE NOTICE '';
END $$;

COMMIT;
EOF

    if [ $? -eq 0 ]; then
        log_success "Demo data loaded successfully"
    else
        log_error "Failed to load demo data"
        exit 1
    fi
}

# Function to verify data loading
verify_data() {
    log_info "Verifying data integrity..."
    
    local verification_result=$(PGPASSWORD="$DB_PASSWORD" psql -h localhost -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT 
            'Users: ' || COUNT(*) FROM users
        UNION ALL
        SELECT 
            'Policies: ' || COUNT(*) FROM policies
        UNION ALL
        SELECT 
            'Agents: ' || COUNT(*) FROM agents
        UNION ALL
        SELECT 
            'Roles: ' || COUNT(*) FROM roles;
    " 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        log_success "Data verification completed:"
        echo "$verification_result"
    else
        log_warning "Data verification failed, but data may still be loaded correctly"
    fi
}

# Main execution function
main() {
    local create_backup=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backup)
                create_backup=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [--backup] [--help]"
                echo ""
                echo "Options:"
                echo "  --backup    Create a backup before recreating"
                echo "  --help      Show this help message"
                echo ""
                echo "This script loads database connection details from .env file"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    log_info "Starting database recreation process..."
    
    # Check prerequisites
    check_psql
    
    # Load environment variables
    load_env_file
    
    # Verify required variables
    if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ] || [ -z "$DB_PASSWORD" ]; then
        log_error "Missing required database environment variables in .env file"
        log_error "Required: DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD"
        exit 1
    fi
    
    # Test connection
    if ! test_connection; then
        exit 1
    fi
    
    # Create backup if requested
    if [ "$create_backup" = true ]; then
        backup_database
    fi
    
    # Confirm with user before proceeding
    echo ""
    log_warning "This will DROP ALL EXISTING TABLES and recreate the database!"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operation cancelled"
        exit 0
    fi
    
    # Execute the recreation process
    drop_all_tables
    create_schema
    load_demo_data
    verify_data
    
    log_success "Database recreation completed successfully!"
    log_info "Your database is now ready with HIPAA compliance demo data."
}

# Run main function with all arguments
main "$@"