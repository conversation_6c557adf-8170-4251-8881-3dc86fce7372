#!/bin/bash
set -e

echo "🔐 Setting up Azure AD Application..."
echo "===================================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "Creating Azure AD app registration"
export APP_ID=$(az ad app create \
  --display-name "${COMPANY_NAME}-healthcare-app" \
  --sign-in-audience "AzureADMyOrg" \
  --web-redirect-uris "http://localhost:3000" \
  --query appId -o tsv)

echo "App ID: $APP_ID"
check_success "Azure AD app created"

# Commenting Service Principal Creation block since service principal already exists
#log_step "Creating service principal"
#az ad sp create --id $APP_ID --only-show-errors
#check_success "Service principal created"

log_step "Generating client secret"
export CLIENT_SECRET=$(az ad app credential reset \
  --id $APP_ID \
  --query password -o tsv)
check_success "Client secret generated"

log_step "Getting tenant information"
export TENANT_ID=$(az account show --query tenantId -o tsv)
echo "Tenant ID: $TENANT_ID"

log_step "Storing secrets in Key Vault"
az keyvault secret set \
  --vault-name "${COMPANY_NAME}-${ENVIRONMENT}-kv" \
  --name "azure-ad-client-id" \
  --value "$APP_ID" \
  --only-show-errors

az keyvault secret set \
  --vault-name "${COMPANY_NAME}-${ENVIRONMENT}-kv" \
  --name "azure-ad-client-secret" \
  --value "$CLIENT_SECRET" \
  --only-show-errors

az keyvault secret set \
  --vault-name "${COMPANY_NAME}-${ENVIRONMENT}-kv" \
  --name "azure-ad-tenant-id" \
  --value "$TENANT_ID" \
  --only-show-errors

check_success "Secrets stored in Key Vault"

# Commenting the permission to Graph APIs and the requisite Admin Consent required for it.
#log_step "Adding Microsoft Graph permissions"
#az ad app permission add \
#  --id $APP_ID \
#  --api ********-0000-0000-c000-************ \
#  --api-permissions e1fe6dd8-ba31-4d61-89e7-88639da4683d=Scope \
#  --only-show-errors
#check_success "Permissions added"

#log_step "Granting admin consent"
#az ad app permission admin-consent --id $APP_ID --only-show-errors
#check_success "Admin consent granted"

# Save to environment file
echo "export APP_ID=\"$APP_ID\"" >> ../configs/environment.conf
echo "export CLIENT_SECRET=\"$CLIENT_SECRET\"" >> ../configs/environment.conf
echo "export TENANT_ID=\"$TENANT_ID\"" >> ../configs/environment.conf

echo ""
echo "🎉 Azure AD setup completed successfully!"
echo "App ID: $APP_ID"
echo "Tenant ID: $TENANT_ID"
echo ""
echo "Next: Run script 5 (Deploy API)"
