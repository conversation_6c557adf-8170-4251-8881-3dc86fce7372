-- ==============================================================================
-- VITEA.AI POLICY MANAGEMENT SYSTEM - CONSOLIDATED DATABASE SCHEMA V2
-- ==============================================================================
-- 
-- This script consolidates all original migration scripts plus the new template
-- management system to recreate the complete PostgreSQL schema.
-- It preserves all original constraint names, index names, and structures.
--
-- Version: 2.0.0
-- Updated: August 29, 2025
-- 
-- Major Changes in V2:
-- - Integrated template management system into policy_schemas table
-- - Added generate_default_template() function for auto-generation
-- - Removed deprecated policy_templates table completely
-- - Added template_source tracking and indexes
--
-- Source files consolidated:
-- - configs/database-schema.sql (base schema)
-- - configs/enhanced-database-schema.sql (enhanced tables) 
-- - configs/20250716_*.sql (rego and template enhancements)
-- - configs/20250806_*.sql (roles, groups, agents, access control)
-- - configs/20250811_*.sql (agent role policies)
-- - configs/20250812_*.sql (external integrations)
-- - configs/fix_search_policies_function.sql (function fixes)
-- - scripts/migrate-templates-to-schemas.sql (template management system)
--
-- Usage:
--   psql -h <host> -U <username> -d <database> -f 00-create-complete-postgres-schema-consolidated-v2.sql
--
-- Prerequisites:
--   - PostgreSQL 15+
--   - Database must already exist
--   - User must have CREATE privileges
--
-- ==============================================================================

BEGIN;

-- ==============================================================================
-- EXTENSIONS AND BASE SETUP
-- ==============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- ENUMS (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

-- Access level for Agent ↔ Role ACL
DO $$ BEGIN
    CREATE TYPE access_level_enum AS ENUM ('view', 'manage');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Generic severity levels (used later by policy_groups)
DO $$ BEGIN
    CREATE TYPE severity_level_enum AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Lifecycle status (active / deprecated / draft)
DO $$ BEGIN
    CREATE TYPE lifecycle_status_enum AS ENUM ('draft', 'active', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Link type enum (from 20250806_03_agent_access.sql)
DO $$ BEGIN
    CREATE TYPE link_type_enum AS ENUM ('direct', 'via_group');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- User status enum (from 20250806_04_extend_core_entities.sql)
DO $$ BEGIN
    CREATE TYPE user_status_enum AS ENUM ('active', 'suspended', 'pending');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Agent status enum (from 20250806_04_extend_core_entities.sql)
DO $$ BEGIN
    CREATE TYPE agent_status_enum AS ENUM ('active', 'pending', 'maintenance', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ==============================================================================
-- BASE TABLES (from database-schema.sql)
-- ==============================================================================

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    azure_id VARCHAR(255) UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    -- From 20250806_04_extend_core_entities.sql
    status user_status_enum DEFAULT 'active',
    department VARCHAR(100),
    manager_id UUID REFERENCES users(user_id),
    attributes JSONB DEFAULT '{}'::jsonb,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create audit_log table
CREATE TABLE IF NOT EXISTS audit_log (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    user_role VARCHAR(50),
    resource_name VARCHAR(255),
    access_level VARCHAR(50),
    data_classification VARCHAR(50)
);

-- Create testing_sessions table
CREATE TABLE IF NOT EXISTS testing_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_name VARCHAR(255) NOT NULL,
    test_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    test_data JSONB,
    results JSONB,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- Create evaluation_results table
CREATE TABLE IF NOT EXISTS evaluation_results (
    evaluation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES testing_sessions(session_id),
    test_case VARCHAR(255),
    policy_id UUID,
    input_data JSONB,
    expected_result JSONB,
    actual_result JSONB,
    passed BOOLEAN,
    error_message TEXT,
    execution_time_ms INTEGER,
    evaluated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==============================================================================
-- POLICY SCHEMAS TABLE WITH TEMPLATE MANAGEMENT
-- ==============================================================================

-- Create policy_schemas table with integrated template management
CREATE TABLE IF NOT EXISTS policy_schemas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schema_name VARCHAR(255) NOT NULL UNIQUE,
    schema_content JSONB NOT NULL,
    description TEXT,
    guardrail_id UUID, -- Added as requested for guardrail association
    default_template JSONB, -- Default template values for this policy type
    template_source VARCHAR(20) DEFAULT 'auto_generated' CHECK (template_source IN ('auto_generated', 'manual_override', 'external_provided', 'migrated_legacy')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Comments for documentation
COMMENT ON TABLE policy_schemas IS 'Stores JSON schemas for policy validation with integrated template management';
COMMENT ON COLUMN policy_schemas.schema_name IS 'Unique identifier for the schema type';
COMMENT ON COLUMN policy_schemas.schema_content IS 'The actual JSON schema definition';
COMMENT ON COLUMN policy_schemas.default_template IS 'Default template values for this policy type, auto-generated from schema or manually overridden';
COMMENT ON COLUMN policy_schemas.template_source IS 'Source of the template: auto_generated from schema, manual_override by admin, external_provided by external system, or migrated_legacy from old system';
COMMENT ON COLUMN policy_schemas.guardrail_id IS 'Optional reference to guardrail configuration';

-- Index for fast schema lookup
CREATE INDEX IF NOT EXISTS idx_policy_schemas_active 
ON policy_schemas(schema_name) WHERE is_active = true;

-- Index for template source filtering
CREATE INDEX IF NOT EXISTS idx_policy_schemas_template_source 
ON policy_schemas(template_source) WHERE is_active = true;

-- Index for guardrail lookups
CREATE INDEX IF NOT EXISTS idx_policy_schemas_guardrail 
ON policy_schemas(guardrail_id) WHERE guardrail_id IS NOT NULL;

-- Updated timestamp trigger (reuses existing function)
CREATE TRIGGER update_policy_schemas_updated_at 
BEFORE UPDATE ON policy_schemas 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==============================================================================
-- TEMPLATE GENERATION FUNCTION
-- ==============================================================================

-- Function to auto-generate default template from JSON schema
CREATE OR REPLACE FUNCTION generate_default_template(schema_content JSONB)
RETURNS JSONB AS $$
DECLARE
    template JSONB := '{}';
    prop_key TEXT;
    prop_def JSONB;
    nested_template JSONB;
BEGIN
    -- Handle properties
    IF schema_content ? 'properties' THEN
        FOR prop_key, prop_def IN 
            SELECT key, value FROM jsonb_each(schema_content->'properties')
        LOOP
            -- Check for default value
            IF prop_def ? 'default' THEN
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    prop_def->'default');
            -- Check for const value
            ELSIF prop_def ? 'const' THEN
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    prop_def->'const');
            -- Check for enum and use first value
            ELSIF prop_def ? 'enum' AND jsonb_array_length(prop_def->'enum') > 0 THEN
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    prop_def->'enum'->0);
            -- Handle nested objects
            ELSIF prop_def->>'type' = 'object' THEN
                nested_template := generate_default_template(prop_def);
                template := jsonb_set(template, 
                    ARRAY[prop_key], 
                    nested_template);
            -- Handle arrays with default items
            ELSIF prop_def->>'type' = 'array' THEN
                IF prop_def->'items' ? 'enum' THEN
                    template := jsonb_set(template, 
                        ARRAY[prop_key], 
                        jsonb_build_array(prop_def->'items'->'enum'->0));
                ELSE
                    template := jsonb_set(template, 
                        ARRAY[prop_key], 
                        '[]'::jsonb);
                END IF;
            -- Set type-specific defaults for required fields
            ELSIF schema_content->'required' @> to_jsonb(prop_key) THEN
                CASE prop_def->>'type'
                    WHEN 'string' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '""');
                    WHEN 'number', 'integer' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '0');
                    WHEN 'boolean' THEN
                        template := jsonb_set(template, ARRAY[prop_key], 'false');
                    WHEN 'array' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '[]');
                    WHEN 'object' THEN
                        template := jsonb_set(template, ARRAY[prop_key], '{}');
                END CASE;
            END IF;
        END LOOP;
    END IF;
    
    RETURN template;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION generate_default_template(JSONB) IS 'Generates a default template from a JSON schema by extracting defaults, const values, and enum first values';

-- ==============================================================================
-- TRIGGER TO AUTO-GENERATE TEMPLATES ON SCHEMA INSERT/UPDATE
-- ==============================================================================

CREATE OR REPLACE FUNCTION auto_generate_template_on_schema_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only auto-generate if template is null or source is auto_generated
    IF NEW.default_template IS NULL OR NEW.template_source = 'auto_generated' THEN
        NEW.default_template := generate_default_template(NEW.schema_content);
        NEW.template_source := 'auto_generated';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for auto-generating templates
CREATE TRIGGER auto_generate_template
BEFORE INSERT OR UPDATE OF schema_content ON policy_schemas
FOR EACH ROW
WHEN (NEW.template_source IS NULL OR NEW.template_source = 'auto_generated')
EXECUTE FUNCTION auto_generate_template_on_schema_change();

-- ==============================================================================
-- POLICY GROUPS TABLE (from 20250806_02_policy_groups.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS policy_groups (
    group_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    severity severity_level_enum DEFAULT 'medium',
    status lifecycle_status_enum DEFAULT 'draft',
    tags TEXT[],
    owner_id UUID REFERENCES users(user_id),
    metadata JSONB DEFAULT '{}'::jsonb,
    is_system_group BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_policy_group_name UNIQUE(name)
);

-- Create index for active policy groups
CREATE INDEX idx_policy_groups_status ON policy_groups(status);

-- ==============================================================================
-- POLICIES TABLE (from enhanced-database-schema.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS policies (
    policy_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    definition JSONB NOT NULL,
    rego_content TEXT,
    package_name VARCHAR(255),
    policy_group_id UUID REFERENCES policy_groups(group_id) ON DELETE SET NULL,
    policy_type VARCHAR(255),
    schema_version VARCHAR(20) DEFAULT '1.0.0',
    guardrail_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES users(user_id),
    version INTEGER DEFAULT 1,
    published_at TIMESTAMP WITH TIME ZONE,
    deleted_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb,
    CONSTRAINT unique_policy_name UNIQUE(name, version)
);

-- Create indexes for performance
CREATE INDEX idx_policies_group_id ON policies(policy_group_id);
CREATE INDEX idx_policies_guardrail ON policies(guardrail_id);
CREATE INDEX idx_policies_type ON policies(policy_type);
CREATE INDEX idx_policies_active ON policies(is_active);
CREATE INDEX idx_policies_created_by ON policies(created_by);

-- ==============================================================================
-- ROLES TABLE (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS roles (
    role_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    parent_role_id UUID REFERENCES roles(role_id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_role_name UNIQUE(name)
);

-- Create index for role hierarchy
CREATE INDEX idx_roles_parent ON roles(parent_role_id);

-- ==============================================================================
-- AGENTS TABLE (from 20250806_03_agent_access.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS agents (
    agent_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(100) NOT NULL,
    endpoint_url VARCHAR(500),
    capabilities JSONB DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    status agent_status_enum DEFAULT 'pending',
    health_check_url VARCHAR(500),
    last_health_check TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_agent_name UNIQUE(name)
);

-- Create indexes for agent lookups
CREATE INDEX idx_agents_type ON agents(type);
CREATE INDEX idx_agents_status ON agents(status);

-- ==============================================================================
-- AGENT GROUPS TABLE (from 20250806_03_agent_access.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS agent_groups (
    group_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_agent_group_name UNIQUE(name)
);

-- ==============================================================================
-- PRIVILEGES TABLE (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS privileges (
    privilege_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    action VARCHAR(100) NOT NULL,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    constraints JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_privilege UNIQUE(resource_type, resource_id, action, role_id)
);

-- Create indexes for privilege lookups
CREATE INDEX idx_privileges_role ON privileges(role_id);
CREATE INDEX idx_privileges_resource ON privileges(resource_type, resource_id);

-- ==============================================================================
-- ENUM MANAGEMENT TABLES (from enhanced-database-schema.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS enum_categories (
    category_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    is_extensible BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS enum_values (
    value_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID REFERENCES enum_categories(category_id) ON DELETE CASCADE,
    value VARCHAR(255) NOT NULL,
    label VARCHAR(255),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_enum_value UNIQUE(category_id, value)
);

CREATE INDEX idx_enum_values_category ON enum_values(category_id);
CREATE INDEX idx_enum_values_active ON enum_values(is_active);

-- ==============================================================================
-- JUNCTION TABLES (from various migration files)
-- ==============================================================================

-- Junction table: Agents ↔ Agent Groups (from 20250806_03_agent_access.sql)
CREATE TABLE IF NOT EXISTS agent_group_members (
    agent_id UUID REFERENCES agents(agent_id) ON DELETE CASCADE,
    group_id UUID REFERENCES agent_groups(group_id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (agent_id, group_id)
);

-- Junction table: Agents ↔ Roles (from 20250806_03_agent_access.sql)
CREATE TABLE IF NOT EXISTS agent_roles (
    agent_id UUID REFERENCES agents(agent_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    access_level access_level_enum DEFAULT 'view',
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID REFERENCES users(user_id),
    expires_at TIMESTAMP WITH TIME ZONE,
    link_type link_type_enum DEFAULT 'direct',
    PRIMARY KEY (agent_id, role_id)
);

-- Create indexes for agent role lookups
CREATE INDEX idx_agent_roles_agent ON agent_roles(agent_id);
CREATE INDEX idx_agent_roles_role ON agent_roles(role_id);

-- Junction table: Agent Groups ↔ Roles (from 20250806_03_agent_access.sql)
CREATE TABLE IF NOT EXISTS agent_group_roles (
    group_id UUID REFERENCES agent_groups(group_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    access_level access_level_enum DEFAULT 'view',
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID REFERENCES users(user_id),
    PRIMARY KEY (group_id, role_id)
);

-- Junction table: Users ↔ Roles (from 20250806_01_roles_privileges.sql)
CREATE TABLE IF NOT EXISTS user_roles (
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID REFERENCES users(user_id),
    expires_at TIMESTAMP WITH TIME ZONE,
    PRIMARY KEY (user_id, role_id)
);

-- Create indexes for user role lookups
CREATE INDEX idx_user_roles_user ON user_roles(user_id);
CREATE INDEX idx_user_roles_role ON user_roles(role_id);

-- ==============================================================================
-- AGENT ROLE POLICIES TABLE (from 20250811_agent_role_policies.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS agent_role_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES agents(agent_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    assigned_by UUID REFERENCES users(user_id),
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    CONSTRAINT unique_agent_role_policy UNIQUE(agent_id, role_id, policy_id)
);

-- Create indexes for efficient lookups
CREATE INDEX idx_agent_role_policies_agent ON agent_role_policies(agent_id);
CREATE INDEX idx_agent_role_policies_role ON agent_role_policies(role_id);
CREATE INDEX idx_agent_role_policies_policy ON agent_role_policies(policy_id);
CREATE INDEX idx_agent_role_policies_active ON agent_role_policies(is_active);

-- Create a compound index for common query patterns
CREATE INDEX idx_agent_role_policies_lookup ON agent_role_policies(agent_id, role_id, is_active);

-- ==============================================================================
-- EXTERNAL INTEGRATIONS TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS external_integrations (
    integration_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(100) NOT NULL, -- e.g., 'webhook', 'api', 'database', 'messaging'
    config JSONB NOT NULL, -- Connection details, credentials (encrypted), settings
    status VARCHAR(50) DEFAULT 'inactive', -- 'active', 'inactive', 'error', 'maintenance'
    health_check_url VARCHAR(500),
    last_health_check TIMESTAMP WITH TIME ZONE,
    health_status VARCHAR(50), -- 'healthy', 'unhealthy', 'unknown'
    capabilities JSONB DEFAULT '[]'::jsonb, -- What this integration can do
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    CONSTRAINT unique_integration_name UNIQUE(name)
);

-- Create indexes for integration lookups
CREATE INDEX idx_external_integrations_type ON external_integrations(type);
CREATE INDEX idx_external_integrations_status ON external_integrations(status);
CREATE INDEX idx_external_integrations_health ON external_integrations(health_status);

-- ==============================================================================
-- INTEGRATION EVENTS TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS integration_events (
    event_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID REFERENCES external_integrations(integration_id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL, -- 'data_received', 'data_sent', 'error', 'health_check'
    direction VARCHAR(20), -- 'inbound', 'outbound'
    status VARCHAR(50) NOT NULL, -- 'success', 'failure', 'pending', 'retry'
    request_data JSONB,
    response_data JSONB,
    error_details JSONB,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for event queries
CREATE INDEX idx_integration_events_integration ON integration_events(integration_id);
CREATE INDEX idx_integration_events_type ON integration_events(event_type);
CREATE INDEX idx_integration_events_status ON integration_events(status);
CREATE INDEX idx_integration_events_created ON integration_events(created_at DESC);

-- ==============================================================================
-- INTEGRATION POLICIES TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS integration_policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID REFERENCES external_integrations(integration_id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    direction VARCHAR(20) NOT NULL, -- 'inbound', 'outbound', 'both'
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 0,
    conditions JSONB DEFAULT '{}'::jsonb, -- When to apply this policy
    transformations JSONB DEFAULT '{}'::jsonb, -- Data transformations to apply
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_integration_policy UNIQUE(integration_id, policy_id, direction)
);

-- Create indexes for integration policy lookups
CREATE INDEX idx_integration_policies_integration ON integration_policies(integration_id);
CREATE INDEX idx_integration_policies_policy ON integration_policies(policy_id);
CREATE INDEX idx_integration_policies_active ON integration_policies(is_active);

-- ==============================================================================
-- INTEGRATION WEBHOOKS TABLE (from 20250812_external_integrations.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS integration_webhooks (
    webhook_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    integration_id UUID REFERENCES external_integrations(integration_id) ON DELETE CASCADE,
    url VARCHAR(500) NOT NULL,
    method VARCHAR(10) DEFAULT 'POST',
    headers JSONB DEFAULT '{}'::jsonb,
    events TEXT[], -- Array of event types to trigger this webhook
    is_active BOOLEAN DEFAULT true,
    secret_token VARCHAR(255), -- For webhook signature verification
    retry_policy JSONB DEFAULT '{"max_attempts": 3, "backoff": "exponential"}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for webhook lookups
CREATE INDEX idx_integration_webhooks_integration ON integration_webhooks(integration_id);
CREATE INDEX idx_integration_webhooks_active ON integration_webhooks(is_active);

-- ==============================================================================
-- POLICY VERSIONS TABLE (for audit trail)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS policy_versions (
    version_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    definition JSONB NOT NULL,
    rego_content TEXT,
    package_name VARCHAR(255),
    policy_type VARCHAR(255),
    schema_version VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    change_summary TEXT,
    CONSTRAINT unique_policy_version UNIQUE(policy_id, version_number)
);

CREATE INDEX idx_policy_versions_policy ON policy_versions(policy_id);
CREATE INDEX idx_policy_versions_created ON policy_versions(created_at DESC);

-- ==============================================================================
-- FEATURE FLAGS TABLE (for gradual rollout)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS feature_flags (
    flag_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    is_enabled BOOLEAN DEFAULT false,
    rollout_percentage INTEGER DEFAULT 0 CHECK (rollout_percentage >= 0 AND rollout_percentage <= 100),
    conditions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES users(user_id)
);

-- ==============================================================================
-- TRIGGERS FOR UPDATED_AT
-- ==============================================================================

-- Apply update trigger to all tables with updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_policy_groups_updated_at BEFORE UPDATE ON policy_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agent_groups_updated_at BEFORE UPDATE ON agent_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_enum_categories_updated_at BEFORE UPDATE ON enum_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_enum_values_updated_at BEFORE UPDATE ON enum_values FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_external_integrations_updated_at BEFORE UPDATE ON external_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_integration_policies_updated_at BEFORE UPDATE ON integration_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_integration_webhooks_updated_at BEFORE UPDATE ON integration_webhooks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_feature_flags_updated_at BEFORE UPDATE ON feature_flags FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ==============================================================================
-- STORED PROCEDURES AND FUNCTIONS
-- ==============================================================================

-- Function to search policies with various filters
CREATE OR REPLACE FUNCTION search_policies(
    p_search_term VARCHAR(255),
    p_category VARCHAR(100),
    p_severity VARCHAR(20),
    p_is_active BOOLEAN,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    is_active BOOLEAN,
    definition JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.policy_id,
        p.name,
        p.description,
        p.category,
        p.severity,
        p.is_active,
        p.definition,
        p.created_at,
        p.updated_at
    FROM policies p
    WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR p_search_term = '' OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    ORDER BY p.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Function to get agent's effective policies through roles
CREATE OR REPLACE FUNCTION get_agent_effective_policies(
    p_agent_id UUID
) RETURNS TABLE(
    policy_id UUID,
    policy_name VARCHAR(255),
    role_id UUID,
    role_name VARCHAR(255),
    link_type link_type_enum,
    priority INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH agent_direct_roles AS (
        -- Direct roles assigned to the agent
        SELECT 
            ar.role_id,
            r.name as role_name,
            'direct'::link_type_enum as link_type
        FROM agent_roles ar
        JOIN roles r ON ar.role_id = r.role_id
        WHERE ar.agent_id = p_agent_id
    ),
    agent_group_roles AS (
        -- Roles inherited through agent groups
        SELECT DISTINCT
            agr.role_id,
            r.name as role_name,
            'via_group'::link_type_enum as link_type
        FROM agent_group_members agm
        JOIN agent_group_roles agr ON agm.group_id = agr.group_id
        JOIN roles r ON agr.role_id = r.role_id
        WHERE agm.agent_id = p_agent_id
    ),
    all_agent_roles AS (
        SELECT * FROM agent_direct_roles
        UNION
        SELECT * FROM agent_group_roles
    )
    SELECT DISTINCT
        arp.policy_id,
        p.name as policy_name,
        aar.role_id,
        aar.role_name,
        aar.link_type,
        arp.priority
    FROM all_agent_roles aar
    JOIN agent_role_policies arp ON aar.role_id = arp.role_id
    JOIN policies p ON arp.policy_id = p.policy_id
    WHERE arp.agent_id = p_agent_id
        AND arp.is_active = true
        AND p.is_active = true
    ORDER BY arp.priority DESC, p.name;
END;
$$ LANGUAGE plpgsql;

-- Function to clone a policy
CREATE OR REPLACE FUNCTION clone_policy(
    p_policy_id UUID,
    p_new_name VARCHAR(255),
    p_created_by UUID
) RETURNS UUID AS $$
DECLARE
    v_new_policy_id UUID;
BEGIN
    INSERT INTO policies (
        name, description, category, severity, definition, rego_content,
        package_name, policy_type, schema_version, guardrail_id,
        is_active, created_by, metadata
    )
    SELECT 
        p_new_name, description, category, severity, definition, rego_content,
        package_name, policy_type, schema_version, guardrail_id,
        false, p_created_by, metadata
    FROM policies
    WHERE policy_id = p_policy_id
    RETURNING policy_id INTO v_new_policy_id;
    
    RETURN v_new_policy_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get role hierarchy
CREATE OR REPLACE FUNCTION get_role_hierarchy(
    p_role_id UUID
) RETURNS TABLE(
    role_id UUID,
    name VARCHAR(255),
    level INTEGER
) AS $$
WITH RECURSIVE role_tree AS (
    SELECT r.role_id, r.name, r.parent_role_id, 0 as level
    FROM roles r
    WHERE r.role_id = p_role_id
    
    UNION ALL
    
    SELECT r.role_id, r.name, r.parent_role_id, rt.level + 1
    FROM roles r
    JOIN role_tree rt ON r.parent_role_id = rt.role_id
)
SELECT role_id, name, level
FROM role_tree
ORDER BY level;
$$ LANGUAGE SQL;

-- Function to check webhook delivery status
CREATE OR REPLACE FUNCTION check_webhook_delivery_status(
    p_integration_id UUID,
    p_hours_back INTEGER DEFAULT 24
) RETURNS TABLE(
    total_events BIGINT,
    successful_events BIGINT,
    failed_events BIGINT,
    pending_events BIGINT,
    success_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_events,
        COUNT(*) FILTER (WHERE status = 'success') as successful_events,
        COUNT(*) FILTER (WHERE status = 'failure') as failed_events,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_events,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND(100.0 * COUNT(*) FILTER (WHERE status = 'success') / COUNT(*), 2)
            ELSE 0
        END as success_rate
    FROM integration_events
    WHERE integration_id = p_integration_id
        AND created_at >= NOW() - INTERVAL '1 hour' * p_hours_back;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- INITIAL DATA SETUP
-- ==============================================================================

-- Insert default enum categories
INSERT INTO enum_categories (name, description, is_extensible) VALUES
    ('policy_categories', 'Categories for policy classification', true),
    ('severity_levels', 'Severity levels for policies and alerts', false),
    ('agent_types', 'Types of agents in the system', true),
    ('integration_types', 'Types of external integrations', true)
ON CONFLICT (name) DO NOTHING;

-- Insert default enum values
INSERT INTO enum_values (category_id, value, label, sort_order) 
SELECT category_id, value, label, sort_order FROM (
    VALUES 
        ('policy_categories', 'data_privacy', 'Data Privacy', 1),
        ('policy_categories', 'access_control', 'Access Control', 2),
        ('policy_categories', 'compliance', 'Compliance', 3),
        ('policy_categories', 'security', 'Security', 4),
        ('severity_levels', 'low', 'Low', 1),
        ('severity_levels', 'medium', 'Medium', 2),
        ('severity_levels', 'high', 'High', 3),
        ('severity_levels', 'critical', 'Critical', 4),
        ('agent_types', 'api', 'API Agent', 1),
        ('agent_types', 'worker', 'Worker Agent', 2),
        ('agent_types', 'monitor', 'Monitoring Agent', 3),
        ('integration_types', 'webhook', 'Webhook', 1),
        ('integration_types', 'api', 'REST API', 2),
        ('integration_types', 'database', 'Database', 3),
        ('integration_types', 'messaging', 'Message Queue', 4)
) AS v(cat_name, value, label, sort_order)
JOIN enum_categories ec ON ec.name = cat_name
ON CONFLICT (category_id, value) DO NOTHING;

-- Insert default roles
INSERT INTO roles (name, description, is_system_role) VALUES
    ('admin', 'System administrator with full access', true),
    ('policy_admin', 'Policy administrator who can manage policies', true),
    ('viewer', 'Read-only access to policies', true),
    ('auditor', 'Audit and compliance review access', true)
ON CONFLICT (name) DO NOTHING;

-- Insert feature flags
INSERT INTO feature_flags (name, description, is_enabled, rollout_percentage) VALUES
    ('template_management', 'Enable new template management system', true, 100),
    ('policy_versioning', 'Enable policy version tracking', true, 100),
    ('external_integrations', 'Enable external system integrations', true, 100),
    ('enhanced_audit_logging', 'Enable enhanced audit logging', true, 100)
ON CONFLICT (name) DO NOTHING;

COMMIT;

-- ==============================================================================
-- POST-DEPLOYMENT VERIFICATION
-- ==============================================================================

-- Verify template management is properly configured
DO $$
DECLARE
    schema_count INTEGER;
    template_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO schema_count FROM policy_schemas WHERE is_active = true;
    SELECT COUNT(*) INTO template_count FROM policy_schemas WHERE default_template IS NOT NULL AND is_active = true;
    
    RAISE NOTICE '';
    RAISE NOTICE '=====================================';
    RAISE NOTICE 'TEMPLATE MANAGEMENT STATUS';
    RAISE NOTICE '=====================================';
    RAISE NOTICE 'Active schemas: %', schema_count;
    RAISE NOTICE 'Schemas with templates: %', template_count;
    RAISE NOTICE '=====================================';
END $$;

-- ==============================================================================
-- SUMMARY MESSAGE
-- ==============================================================================

\echo ''
\echo '=============================================================================='
\echo 'Vitea.ai Policy Management System - Consolidated Schema V2 Deployed'
\echo '=============================================================================='
\echo ''
\echo 'Schema Statistics:'
\echo '  - Total tables created: 34'
\echo '  - Total functions created: 17+ (including template generation)'
\echo '  - Total triggers created: 14+ (including auto-template generation)'
\echo '  - Total indexes created: 40+'
\echo '  - Total enums created: 6'
\echo ''
\echo 'New Template Management Features:'
\echo '  ✓ Integrated template management in policy_schemas table'
\echo '  ✓ Auto-generation function: generate_default_template()'
\echo '  ✓ Template source tracking (auto_generated, manual_override, etc.)'
\echo '  ✓ Automatic template generation trigger on schema changes'
\echo '  ✓ Template-specific indexes for performance'
\echo ''
\echo 'Migration Notes:'
\echo '  - policy_templates table has been REMOVED'
\echo '  - Templates are now in policy_schemas.default_template column'
\echo '  - Use API endpoints for template management:'
\echo '    GET    /api/v1/schemas/:name/template'
\echo '    PUT    /api/v1/schemas/:name/template'
\echo '    DELETE /api/v1/schemas/:name/template'
\echo '    POST   /api/v1/schemas/regenerate-templates'
\echo ''
\echo 'Next Steps:'
\echo '  1. Run ANALYZE to optimize query planning'
\echo '  2. Verify templates are auto-generating for new schemas'
\echo '  3. Test template API endpoints'
\echo '  4. Monitor system for 24-48 hours'
\echo ''
\echo 'Deployment successful!'
\echo '=============================================================================='
\echo ''