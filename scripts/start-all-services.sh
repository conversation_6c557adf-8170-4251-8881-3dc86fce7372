#!/bin/bash

# Script to start all Vitea services across separate repositories
# Services are started in dependency order to ensure proper initialization
#
# USAGE:
#   ./start-all-services.sh                 # Default: only start containers that aren't running
#   ./start-all-services.sh --force-restart # Stop and restart all containers
#   ./start-all-services.sh --force-build   # Stop, rebuild images, and restart all containers
#
# ENVIRONMENT SETUP:
# This script always uses .env files in each repository root.
# Before running, copy the appropriate environment file to .env:
#   cp .env.example .env      (for local development)
#   cp .env.dev .env          (for development VM)  
#   cp .env.production .env   (for production)
#
# DEPENDENCY ORDER:
# 1. Evaluators (eval-api) - Foundation evaluation services
# 2. Policy Runtime - Policy evaluation containers
# 3. PII Guard - PII protection services  
# 4. Pilot - Core application services
# 5. Supporting Services - Envoy, MCP, Testing platform (parallel)

set -e

# Parse command line arguments
FORCE_RESTART=false
FORCE_BUILD=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --force-restart)
            FORCE_RESTART=true
            shift
            ;;
        --force-build)
            FORCE_BUILD=true
            FORCE_RESTART=true  # force-build implies force-restart
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --force-restart    Stop and restart all containers (preserves data)"
            echo "  --force-build      Stop, rebuild images, and restart all containers"
            echo "  -h, --help         Show this help message"
            echo ""
            echo "Default behavior: Only start containers that aren't already running"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Phase 1: Start all foundation services in parallel
declare -a FOUNDATION_SERVICES=(
    # Start evaluators, policy runtime, and supporting services all at once
    "../testing-observability-platform:Testing & Evaluation Platform (includes eval API):http://localhost:9000/health,http://localhost:9002/:30:"
    "../policy_runtime:Policy Runtime:http://localhost:8000/api/v1/health:25:docker-compose.full.yml"
    "../envoy_fhir_proxy:Envoy FHIR Proxy:http://localhost:9901/ready:10:"
    "../vitea-mcp-server:MCP Server:http://localhost:8002/health:10:"
)

# Phase 2: Services that depend on foundation services
declare -a DEPENDENT_SERVICES=(
    # Start PII Guard once policy runtime is confirmed running
    "../PiiGuard:PII Guard:http://localhost:8080/health:20:docker-compose.dev.yml"
)

# Phase 3: Services that depend on eval-api being ready
declare -a PILOT_SERVICES=(
    # Start Pilot once eval-api is confirmed running
    ".:Pilot Services:http://localhost:8001/health:15:"
)

# Function to start a service and wait for completion
start_service() {
    local repo_path=$1
    local service_name=$2
    local startup_delay=${3:-15}
    local custom_compose_file=${4:-}
    local log_file="/tmp/vitea-$(echo $service_name | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')-startup.log"
    
    if [ ! -d "$repo_path" ]; then
        error "Repository not found: $repo_path"
        return 1
    fi
    
    log "Starting $service_name in $repo_path..."
    
    cd "$repo_path"
    
    # Check if .env file exists (warn but don't fail - some services might not need it)
    if [ ! -f ".env" ] && [ -f ".env.example" ]; then
        warn "No .env file found in $repo_path, but .env.example exists"
        warn "Consider copying: cp .env.example .env"
    fi
    
    # Determine which compose file to use
    local compose_file=""
    if [ -n "$custom_compose_file" ]; then
        if [ -f "$custom_compose_file" ]; then
            compose_file="$custom_compose_file"
            log "Using custom compose file: $compose_file"
        else
            error "Custom compose file not found: $custom_compose_file"
            return 1
        fi
    elif [ -f "docker-compose.yml" ]; then
        compose_file="docker-compose.yml"
    elif [ -f "docker-compose.yaml" ]; then
        compose_file="docker-compose.yaml"
    else
        error "No docker-compose file found in $repo_path"
        return 1
    fi
    
    # Handle different startup modes
    if [ "$FORCE_RESTART" = true ]; then
        log "Force restart requested - stopping previous containers for $service_name..."
        docker-compose -f "$compose_file" down 2>/dev/null || true
    fi
    
    # Start the service with appropriate options
    if [ "$FORCE_BUILD" = true ]; then
        log "Force build requested - building and launching containers for $service_name..."
        if ! docker-compose -f "$compose_file" up --build -d > "$log_file" 2>&1; then
            error "Failed to build and start $service_name. Check log: $log_file"
            return 1
        fi
    elif [ "$FORCE_RESTART" = true ]; then
        log "Launching containers for $service_name..."
        if ! docker-compose -f "$compose_file" up -d > "$log_file" 2>&1; then
            error "Failed to start $service_name. Check log: $log_file"
            return 1
        fi
    else
        log "Launching containers for $service_name (only if not already running)..."
        if ! docker-compose -f "$compose_file" up -d > "$log_file" 2>&1; then
            error "Failed to start $service_name. Check log: $log_file"
            return 1
        fi
    fi
    
    if [ -n "$custom_compose_file" ]; then
        success "$service_name containers launch initiated (compose: $custom_compose_file, logs: $log_file)"
    else
        success "$service_name containers launch initiated (compose: $compose_file, logs: $log_file)"
    fi
    
    # Wait for startup delay
    if [ "$startup_delay" -gt 0 ]; then
        log "Waiting ${startup_delay}s for $service_name to initialize..."
        sleep "$startup_delay"
    fi
    
    # Return to original directory
    cd - > /dev/null
    
    return 0
}

# Function to start services in parallel (for supporting services)
start_service_parallel() {
    local repo_path=$1
    local service_name=$2
    local startup_delay=${3:-10}
    local compose_file=${4:-}
    local log_file="/tmp/vitea-$(echo $service_name | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9]/-/g' | sed 's/--*/-/g' | sed 's/^-\|-$//g')-startup.log"
    
    # Start service in background
    (
        start_service "$repo_path" "$service_name" "$startup_delay" "$compose_file"
    ) &
    
    local pid=$!
    echo "$pid:$service_name:$log_file" >> /tmp/vitea-services.pid
    
    log "$service_name starting in parallel (PID: $pid)"
    return 0
}

# Function to check if service is ready (supports multiple comma-separated URLs)
check_service() {
    local service_name=$1
    local urls=$2
    local max_attempts=${3:-30}
    
    if [ -z "$urls" ]; then
        log "No health check URLs provided for $service_name, skipping health check"
        return 0
    fi
    
    # Convert comma-separated URLs to array
    IFS=',' read -ra url_array <<< "$urls"
    local total_urls=${#url_array[@]}
    
    log "Checking $service_name at $total_urls endpoint(s): $urls"
    
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        # Check if we received a signal to exit
        if [ -f "/tmp/vitea-shutdown" ]; then
            log "Shutdown signal received, stopping health check for $service_name"
            return 1
        fi
        
        local all_healthy=true
        local status_summary=""
        
        # Check each URL
        for url in "${url_array[@]}"; do
            # Trim whitespace
            url=$(echo "$url" | xargs)
            
            # Special handling for different endpoint types
            local status_code=0
            if [[ "$url" == *"/sse/"* ]]; then
                # For SSE endpoints, check if we get a 200 status code
                status_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 2 --max-time 3 "$url" 2>/dev/null || echo "000")
            else
                # For regular HTTP endpoints
                status_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 2 --max-time 5 "$url" 2>/dev/null || echo "000")
            fi
            
            if [ "$status_code" = "200" ] || [ "$status_code" = "204" ]; then
                status_summary="$status_summary $url:✓"
            else
                status_summary="$status_summary $url:✗($status_code)"
                all_healthy=false
            fi
        done
        
        if [ "$all_healthy" = true ]; then
            success "$service_name is ready! All endpoints healthy:$status_summary"
            return 0
        fi
        
        log "Attempt $attempt/$max_attempts - $service_name not fully ready:$status_summary"
        
        # Sleep with interruption check
        for i in {1..3}; do
            if [ -f "/tmp/vitea-shutdown" ]; then
                return 1
            fi
            sleep 1
        done
        
        ((attempt++))
    done
    
    warn "$service_name health check failed after $max_attempts attempts. Final status:$status_summary"
    return 1
}

# Function to check Docker container status for a service
check_containers() {
    local repo_path=$1
    local service_name=$2
    local compose_file=${3:-"docker-compose.yml"}
    
    if [ ! -d "$repo_path" ]; then
        error "Repository not found: $repo_path"
        return 1
    fi
    
    cd "$repo_path"
    
    # Determine which compose file to use
    if [ ! -f "$compose_file" ]; then
        if [ -f "docker-compose.yml" ]; then
            compose_file="docker-compose.yml"
        elif [ -f "docker-compose.yaml" ]; then
            compose_file="docker-compose.yaml"
        else
            error "No docker-compose file found in $repo_path"
            cd - > /dev/null
            return 1
        fi
    fi
    
    log "Checking container status for $service_name using $compose_file..."
    
    # Get all containers from this compose file
    local containers=$(docker-compose -f "$compose_file" ps -q 2>/dev/null)
    
    if [ -z "$containers" ]; then
        warn "No containers found for $service_name"
        cd - > /dev/null
        return 1
    fi
    
    local all_running=true
    local container_status=""
    local total_containers=0
    local running_containers=0
    
    # Check each container
    for container_id in $containers; do
        ((total_containers++))
        
        # Get container name and status
        local container_name=$(docker inspect --format='{{.Name}}' "$container_id" 2>/dev/null | sed 's/^.//')
        local container_state=$(docker inspect --format='{{.State.Status}}' "$container_id" 2>/dev/null)
        local container_health=$(docker inspect --format='{{.State.Health.Status}}' "$container_id" 2>/dev/null)
        
        if [ "$container_state" = "running" ]; then
            ((running_containers++))
            if [ "$container_health" = "healthy" ] || [ "$container_health" = "<no value>" ]; then
                container_status="$container_status $container_name:✓"
            else
                container_status="$container_status $container_name:⚠($container_health)"
                if [ "$container_health" = "unhealthy" ]; then
                    all_running=false
                fi
            fi
        else
            container_status="$container_status $container_name:✗($container_state)"
            all_running=false
        fi
    done
    
    cd - > /dev/null
    
    if [ "$all_running" = true ]; then
        success "$service_name containers: $running_containers/$total_containers running$container_status"
        return 0
    else
        warn "$service_name containers: $running_containers/$total_containers running$container_status"
        return 1
    fi
}

# Cleanup function for interruption during startup
cleanup_on_interrupt() {
    # Signal shutdown to health checks
    touch /tmp/vitea-shutdown
    
    log "Startup interrupted! Cleaning up..."
    
    # Kill any background startup processes
    if [ -f "/tmp/vitea-services.pid" ]; then
        while IFS=':' read -r pid service_name log_file; do
            if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
                log "Stopping $service_name startup process (PID: $pid)..."
                kill "$pid" 2>/dev/null || true
            fi
        done < /tmp/vitea-services.pid
        rm -f /tmp/vitea-services.pid
    fi
    
    # Clean up temporary files
    rm -f /tmp/vitea-shutdown /tmp/vitea-*-startup.log
    
    error "Startup was interrupted. To clean up any partially started services, run: ./stop-all-services.sh"
    exit 1
}

# Set up signal handlers (only during startup)
trap cleanup_on_interrupt INT TERM

echo "🚀 Vitea Distributed Service Starter"
echo "====================================="

# Show current mode
if [ "$FORCE_BUILD" = true ]; then
    echo "Mode: FORCE BUILD (stop, rebuild images, restart all)"
elif [ "$FORCE_RESTART" = true ]; then
    echo "Mode: FORCE RESTART (stop and restart all containers)"
else
    echo "Mode: DEFAULT (start only containers that aren't running)"
fi

echo "Starting services in new dependency order:"
echo "1. Foundation Services (parallel) → 2. PII Guard → 3. Pilot Services"
echo ""

# Clean up any existing temporary files
rm -f /tmp/vitea-shutdown /tmp/vitea-services.pid /tmp/vitea-*-startup.log

# Ensure required Docker networks exist
log "=== PHASE 0: Setting up Docker infrastructure ==="
if ! docker network inspect vitea-shared-network >/dev/null 2>&1; then
    log "Creating vitea-shared-network..."
    if docker network create vitea-shared-network >/dev/null 2>&1; then
        success "Created vitea-shared-network"
    else
        error "Failed to create vitea-shared-network"
        exit 1
    fi
else
    log "vitea-shared-network already exists"
fi

# Parse service config helper function
parse_service_config() {
    local service_config=$1
    local field_name=$2
    
    # Count total fields to handle parsing correctly
    field_count=$(echo "$service_config" | tr -cd ':' | wc -c)
    field_count=$((field_count + 1))
    
    case $field_name in
        "repo_path")
            echo "$service_config" | cut -d':' -f1
            ;;
        "service_name")
            echo "$service_config" | cut -d':' -f2
            ;;
        "health_url")
            if [ $field_count -ge 5 ]; then
                # Health URL is everything between service_name and startup_delay
                echo "$service_config" | cut -d':' -f3- | rev | cut -d':' -f3- | rev
            else
                # Old format: Health URL is everything between service_name and startup_delay  
                echo "$service_config" | cut -d':' -f3- | rev | cut -d':' -f2- | rev
            fi
            ;;
        "startup_delay")
            if [ $field_count -ge 5 ]; then
                echo "$service_config" | rev | cut -d':' -f2 | rev
            else
                echo "$service_config" | rev | cut -d':' -f1 | rev
            fi
            ;;
        "compose_file")
            if [ $field_count -ge 5 ]; then
                echo "$service_config" | rev | cut -d':' -f1 | rev
            else
                echo ""
            fi
            ;;
    esac
}

log "=== PHASE 1: Starting Foundation Services in Parallel ==="
# Start all foundation services in parallel
for service_config in "${FOUNDATION_SERVICES[@]}"; do
    repo_path=$(parse_service_config "$service_config" "repo_path")
    service_name=$(parse_service_config "$service_config" "service_name")
    startup_delay=$(parse_service_config "$service_config" "startup_delay")
    compose_file=$(parse_service_config "$service_config" "compose_file")
    
    start_service_parallel "$repo_path" "$service_name" "$startup_delay" "$compose_file"
done

# Wait for foundation services to complete startup
log "Waiting for foundation services to complete startup..."
if [ -f "/tmp/vitea-services.pid" ]; then
    while IFS=':' read -r pid service_name log_file; do
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            wait "$pid"
            if [ $? -eq 0 ]; then
                success "$service_name containers launch completed"
            else
                warn "$service_name containers launch completed with warnings"
            fi
        fi
    done < /tmp/vitea-services.pid
    rm -f /tmp/vitea-services.pid
fi

# Wait for policy runtime to be ready before starting PII Guard
log "=== Checking Policy Runtime Ready ==="
if ! check_service "Policy Runtime" "http://localhost:8000/api/v1/health" 60; then
    error "Policy Runtime is not ready after 60 attempts - aborting"
    exit 1
fi

log "=== PHASE 2: Starting PII Guard (depends on Policy Runtime) ==="
for service_config in "${DEPENDENT_SERVICES[@]}"; do
    repo_path=$(parse_service_config "$service_config" "repo_path")
    service_name=$(parse_service_config "$service_config" "service_name")
    health_url=$(parse_service_config "$service_config" "health_url")
    startup_delay=$(parse_service_config "$service_config" "startup_delay")
    compose_file=$(parse_service_config "$service_config" "compose_file")
    
    if ! start_service "$repo_path" "$service_name" "$startup_delay" "$compose_file"; then
        error "Failed to start $service_name - aborting startup"
        exit 1
    fi
    
    # Check container status
    if ! check_containers "$repo_path" "$service_name" "$compose_file"; then
        warn "$service_name has container issues, but continuing..."
    fi
    
    # Check health if URL provided
    if [ -n "$health_url" ]; then
        if ! check_service "$service_name" "$health_url"; then
            warn "$service_name health check failed, but continuing..."
        fi
    fi
done

# Wait for eval-api to be ready before starting Pilot
log "=== Checking Eval API Ready ==="
if ! check_service "Eval API" "http://localhost:9000/health" 60; then
    error "Eval API is not ready after 60 attempts - aborting"
    exit 1
fi

log "=== PHASE 3: Starting Pilot Services (depends on Eval API) ==="
for service_config in "${PILOT_SERVICES[@]}"; do
    repo_path=$(parse_service_config "$service_config" "repo_path")
    service_name=$(parse_service_config "$service_config" "service_name")
    health_url=$(parse_service_config "$service_config" "health_url")
    startup_delay=$(parse_service_config "$service_config" "startup_delay")
    compose_file=$(parse_service_config "$service_config" "compose_file")
    
    if ! start_service "$repo_path" "$service_name" "$startup_delay" "$compose_file"; then
        error "Failed to start $service_name - aborting startup"
        exit 1
    fi
    
    # Check container status
    if ! check_containers "$repo_path" "$service_name" "$compose_file"; then
        warn "$service_name has container issues, but continuing..."
    fi
    
    # Check health if URL provided
    if [ -n "$health_url" ]; then
        if ! check_service "$service_name" "$health_url"; then
            warn "$service_name health check failed, but continuing..."
        fi
    fi
done

log "=== PHASE 4: Final System Status Summary ==="

# Function to get container summary for a service
get_container_summary() {
    local repo_path=$1
    local compose_file=${2:-"docker-compose.yml"}
    
    if [ ! -d "$repo_path" ]; then
        return 1
    fi
    
    cd "$repo_path"
    
    # Determine which compose file to use
    if [ ! -f "$compose_file" ]; then
        if [ -f "docker-compose.yml" ]; then
            compose_file="docker-compose.yml"
        elif [ -f "docker-compose.yaml" ]; then
            compose_file="docker-compose.yaml"
        else
            cd - > /dev/null
            return 1
        fi
    fi
    
    # Get all containers from this compose file
    local containers=$(docker-compose -f "$compose_file" ps -q 2>/dev/null)
    
    if [ -z "$containers" ]; then
        cd - > /dev/null
        return 1
    fi
    
    # Check each container and output status
    for container_id in $containers; do
        local container_name=$(docker inspect --format='{{.Name}}' "$container_id" 2>/dev/null | sed 's/^.//')
        local container_state=$(docker inspect --format='{{.State.Status}}' "$container_id" 2>/dev/null)
        local container_health=$(docker inspect --format='{{.State.Health.Status}}' "$container_id" 2>/dev/null)
        
        local status_icon="✗"
        local health_status=""
        
        if [ "$container_state" = "running" ]; then
            status_icon="✓"
            if [ "$container_health" != "<no value>" ] && [ -n "$container_health" ]; then
                if [ "$container_health" = "healthy" ]; then
                    health_status=" (healthy)"
                else
                    health_status=" ($container_health)"
                fi
            fi
        else
            health_status=" ($container_state)"
        fi
        
        echo "  $status_icon $container_name$health_status"
    done
    
    cd - > /dev/null
    return 0
}

# Function to check endpoint health status
get_endpoint_health() {
    local urls=$1
    
    if [ -z "$urls" ]; then
        echo ""
        return 0
    fi
    
    # Convert comma-separated URLs to array
    IFS=',' read -ra url_array <<< "$urls"
    
    local all_healthy=true
    for url in "${url_array[@]}"; do
        url=$(echo "$url" | xargs)
        
        local status_code=0
        if [[ "$url" == *"/sse/"* ]]; then
            status_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 2 --max-time 3 "$url" 2>/dev/null || echo "000")
        else
            status_code=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 2 --max-time 5 "$url" 2>/dev/null || echo "000")
        fi
        
        if [ "$status_code" != "200" ] && [ "$status_code" != "204" ]; then
            all_healthy=false
            break
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        echo " - Endpoints: ✓ Healthy"
    else
        echo " - Endpoints: ✗ Unhealthy"
    fi
}

# Generate comprehensive status report
log "=== Container and Service Status ==="
all_healthy=true
all_containers_running=true

for service_config in "${FOUNDATION_SERVICES[@]}" "${DEPENDENT_SERVICES[@]}" "${PILOT_SERVICES[@]}"; do
    # Parse the config using helper function
    repo_path=$(parse_service_config "$service_config" "repo_path")
    service_name=$(parse_service_config "$service_config" "service_name")
    health_url=$(parse_service_config "$service_config" "health_url")
    compose_file=$(parse_service_config "$service_config" "compose_file")
    
    log "$service_name:"
    
    # Get container status summary
    if ! get_container_summary "$repo_path" "$compose_file"; then
        log "  ✗ No containers found"
        all_containers_running=false
    else
        # Check if any containers are not running by looking at the output
        container_output=$(get_container_summary "$repo_path" "$compose_file")
        if echo "$container_output" | grep -q "✗"; then
            all_containers_running=false
        fi
        echo "$container_output"
    fi
    
    # Get endpoint health summary
    if [ -n "$health_url" ]; then
        endpoint_status=$(get_endpoint_health "$health_url")
        echo "$endpoint_status"
        if echo "$endpoint_status" | grep -q "✗"; then
            all_healthy=false
        fi
    fi
    
    echo ""
done

echo ""
if [ "$all_healthy" = true ] && [ "$all_containers_running" = true ]; then
    success "🎉 All services are running and healthy!"
    echo ""
    log "=== Service Endpoints ==="
    log "  • Evaluation API: http://localhost:9000 (Testing platform API)"
    log "  • Evaluators Service: http://localhost:9002 (AI Evaluators)"
    log "  • Policy Runtime: http://localhost:8080/health"
    log "  • PII Guard: http://localhost:8080/health"
    log "  • Pilot API: http://localhost:8001/health"
    log "  • Pilot Chatbot: http://localhost:3001"
    log "  • Pilot Admin: http://localhost:3001/admin/"
    log "  • MCP Server: http://localhost:8002/health/"
    log "  • Envoy Proxy: http://localhost:8081"
    log "  • Envoy Admin: http://localhost:9901/ready"
    echo ""
    log "Ready for integration testing!"
    log "To stop all services, run: ./stop-all-services.sh"
    echo ""
    
    # Clean up temporary files but leave services running
    rm -f /tmp/vitea-shutdown /tmp/vitea-services.pid /tmp/vitea-*-startup.log
    
    success "✅ Distributed system startup complete!"
    
    # Remove trap since we're done with startup
    trap - INT TERM
else
    if [ "$all_containers_running" = false ]; then
        error "Some Docker containers are not running properly"
    fi
    if [ "$all_healthy" = false ]; then
        error "Some services failed endpoint health checks"
    fi
    echo ""
    log "Check the log files for details:"
    ls -la /tmp/vitea-*-startup.log 2>/dev/null || true
    echo ""
    log "Services may still be starting. Wait a few minutes and check endpoints manually."
    log "To check container status: docker ps -a"
    log "To check service logs: docker-compose logs [service-name]"
    log "To clean up and restart, run: ./stop-all-services.sh && ./start-all-services.sh"
    exit 1
fi
