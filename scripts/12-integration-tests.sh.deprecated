#!/bin/bash
set -e

echo "🧪 Running Integration Tests..."
echo "==============================="

source ../configs/environment.conf

test_passed=0
test_failed=0

run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    echo -n "Testing $test_name... "
    
    result=$(eval "$test_command" 2>/dev/null || echo "FAILED")
    
    if [[ "$result" == "$expected_result" ]]; then
        echo "✅ PASSED"
        ((test_passed++))
    else
        echo "❌ FAILED (Expected: $expected_result, Got: $result)"
        ((test_failed++))
    fi
}

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

log_step "Testing Database Connectivity"
DB_TEST=$(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST}" \
    -p 5432 -d vitea_db -U dbadmin \
    -t -c "SELECT 1;" --set sslmode=require 2>/dev/null | xargs || echo "FAILED")

run_test "Database Connection" "echo $DB_TEST" "1"

log_step "Testing New Database Tables"
NEW_TABLE_COUNT=$(PGPASSWORD="$DB_PASSWORD" psql -h "${DB_HOST}" \
    -p 5432 -d vitea_db -U dbadmin \
    -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('mcp_chat_sessions', 'mcp_flow_steps', 'chat_messages');" \
    --set sslmode=require 2>/dev/null | xargs || echo "0")

run_test "New Tables Created" "echo $NEW_TABLE_COUNT" "3"

log_step "Testing Enhanced API Endpoints"
API_URL="https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net"

run_test "API Health Check" "curl -s -o /dev/null -w '%{http_code}' '$API_URL/health'" "200"
run_test "Enhanced API Version" "curl -s '$API_URL/api/v1/test' | jq -r '.version' 2>/dev/null || echo 'FAILED'" "2.0.0"
run_test "Policy Management Endpoint" "curl -s -o /dev/null -w '%{http_code}' '$API_URL/api/v1/policies' -H 'Authorization: Bearer admin-token'" "200"
run_test "Metrics Endpoint" "curl -s -o /dev/null -w '%{http_code}' '$API_URL/api/v1/metrics' -H 'Authorization: Bearer admin-token'" "200"

log_step "Testing Chat Session Creation"
SESSION_RESPONSE=$(curl -s -X POST "$API_URL/api/v1/chat/session" \
  -H "Content-Type: application/json" \
  -H "X-User-Id: test-user" \
  -d '{"metadata":{"test":true}}' || echo "FAILED")

if [[ "$SESSION_RESPONSE" != "FAILED" ]]; then
    SESSION_ID=$(echo "$SESSION_RESPONSE" | jq -r '.session_id' 2>/dev/null || echo "FAILED")
    run_test "Chat Session Creation" "echo $SESSION_ID | grep -E '^[0-9a-f-]{36}$' && echo 'PASSED' || echo 'FAILED'" "PASSED"
    
    if [[ "$SESSION_ID" != "FAILED" ]]; then
        log_step "Testing MCP Flow Processing"
        CHAT_RESPONSE=$(curl -s -X POST "$API_URL/api/v1/chat/$SESSION_ID/message" \
          -H "Content-Type: application/json" \
          -H "X-User-Id: test-user" \
          -d '{"message":"What is diabetes?"}' || echo "FAILED")
        
        run_test "MCP Chat Processing" "echo '$CHAT_RESPONSE' | jq -r '.message' | grep -q 'mock response' && echo 'PASSED' || echo 'FAILED'" "PASSED"
    fi
else
    run_test "Chat Session Creation" "echo 'FAILED'" "PASSED"
fi

log_step "Testing Frontend Applications"
FRONTEND_URL="https://${COMPANY_NAME}-${ENVIRONMENT}-frontend.azurewebsites.net"
if [ -n "${ADMIN_FRONTEND_URL}" ]; then
    ADMIN_URL="${ADMIN_FRONTEND_URL}"
else
    ADMIN_URL="https://${COMPANY_NAME}-${ENVIRONMENT}-admin.azurewebsites.net"
fi

run_test "Main Frontend" "curl -s -o /dev/null -w '%{http_code}' '$FRONTEND_URL'" "200"
run_test "Admin Interface" "curl -s -o /dev/null -w '%{http_code}' '$ADMIN_URL'" "200"

log_step "Testing Policy Templates"
TEMPLATE_COUNT=$(curl -s "$API_URL/api/v1/policy-templates" \
  -H "Authorization: Bearer admin-token" | jq length 2>/dev/null || echo "0")

run_test "Policy Templates Loaded" "echo $TEMPLATE_COUNT | awk '\$1 >= 3 {print \"PASSED\"} \$1 < 3 {print \"FAILED\"}'" "PASSED"

log_step "Testing System Metrics"
METRICS_RESPONSE=$(curl -s "$API_URL/api/v1/metrics" \
  -H "Authorization: Bearer admin-token" || echo "FAILED")

run_test "System Metrics API" "echo '$METRICS_RESPONSE' | jq -r '.current_stats.total_policies' | grep -E '^[0-9]+$' && echo 'PASSED' || echo 'FAILED'" "PASSED"

log_step "Performance Tests"
API_RESPONSE_TIME=$(curl -s -w "%{time_total}" -o /dev/null "$API_URL/health" | awk '{if ($1 < 2.0) print "PASSED"; else print "FAILED"}')
run_test "API Response Time < 2s" "echo $API_RESPONSE_TIME" "PASSED"

echo ""
echo "🏁 INTEGRATION TEST RESULTS"
echo "=========================="
echo "✅ Tests Passed: $test_passed"
echo "❌ Tests Failed: $test_failed"
echo "📊 Success Rate: $(( test_passed * 100 / (test_passed + test_failed) ))%"

if [ $test_failed -eq 0 ]; then
    echo ""
    echo "🎉 ALL TESTS PASSED! Your enhanced Vitea application is ready for production!"
    echo ""
    echo "🔗 Application URLs:"
    echo "   🌐 Main App: $FRONTEND_URL"
    echo "   🤖 AI Chatbot: $FRONTEND_URL/chatbot"
    echo "   ⚙️ Admin Interface: $ADMIN_URL"
    echo "   🔧 API: $API_URL"
    echo ""
    echo "📚 Next Steps:"
    echo "   1. Configure Azure OpenAI API keys in the API app settings"
    echo "   2. Set up user roles and permissions in Azure AD"
    echo "   3. Configure additional policies in the admin interface"
    echo "   4. Train your team on the new features"
    echo "   5. Monitor system performance and policy violations"
else
    echo ""
    echo "⚠️ Some tests failed. Please review the issues above before proceeding to production."
    exit 1
fi