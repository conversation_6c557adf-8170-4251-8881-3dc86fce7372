-- ==============================================================================
-- VITEA.AI POLICY MANAGEMENT SYSTEM - CONSOLIDATED DATABASE SCHEMA
-- ==============================================================================
-- 
-- This script consolidates all original migration scripts to recreate the 
-- complete PostgreSQL schema exactly as defined in the original configs.
-- It preserves all original constraint names, index names, and structures.
--
-- Source files consolidated:
-- - configs/database-schema.sql (base schema)
-- - configs/enhanced-database-schema.sql (enhanced tables) 
-- - configs/20250716_*.sql (rego and template enhancements)
-- - configs/20250806_*.sql (roles, groups, agents, access control)
-- - configs/20250811_*.sql (agent role policies)
-- - configs/20250812_*.sql (external integrations)
-- - configs/fix_search_policies_function.sql (function fixes)
-- - configs/policy_templates.sql (policy template data)
-- - scripts/create-complete-schema.sql (testing/evaluation tables)
--
-- Usage:
--   psql -h <host> -U <username> -d <database> -f 00-create-complete-postgres-schema-consolidated.sql
--
-- Prerequisites:
--   - PostgreSQL 15+
--   - Database must already exist
--   - User must have CREATE privileges
--
-- Generated from original migration files - no modifications made
-- ==============================================================================

BEGIN;

-- ==============================================================================
-- EXTENSIONS AND BASE SETUP
-- ==============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- ENUMS (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

-- Access level for Agent ↔ Role ACL
DO $$ BEGIN
    CREATE TYPE access_level_enum AS ENUM ('view', 'manage');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Generic severity levels (used later by policy_groups)
DO $$ BEGIN
    CREATE TYPE severity_level_enum AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Lifecycle status (active / deprecated / draft)
DO $$ BEGIN
    CREATE TYPE lifecycle_status_enum AS ENUM ('draft', 'active', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Link type enum (from 20250806_03_agent_access.sql)
DO $$ BEGIN
    CREATE TYPE link_type_enum AS ENUM ('direct', 'via_group');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- User status enum (from 20250806_04_extend_core_entities.sql)
DO $$ BEGIN
    CREATE TYPE user_status_enum AS ENUM ('active', 'suspended', 'pending');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Agent status enum (from 20250806_04_extend_core_entities.sql)
DO $$ BEGIN
    CREATE TYPE agent_status_enum AS ENUM ('active', 'pending', 'maintenance', 'deprecated');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ==============================================================================
-- BASE TABLES (from database-schema.sql)
-- ==============================================================================

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    azure_ad_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(50) NOT NULL DEFAULT 'user',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id)
);

-- Add extensions from 20250806_04_extend_core_entities.sql
ALTER TABLE users
    ADD COLUMN IF NOT EXISTS department VARCHAR(255),
    ADD COLUMN IF NOT EXISTS status user_status_enum DEFAULT 'active',
    ADD COLUMN IF NOT EXISTS risk_score NUMERIC(4,2) DEFAULT 0.0,
    ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE,
    ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN DEFAULT false;

-- Create policies table
CREATE TABLE IF NOT EXISTS policies (
    policy_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    policy_type VARCHAR(50) DEFAULT 'opa',
    definition JSONB NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN DEFAULT false,
    severity VARCHAR(20) DEFAULT 'medium',
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id),
    UNIQUE(name, version)
);

-- Add rego columns from 20250716_01__add_rego_blob_columns.sql
ALTER TABLE policies 
    ADD COLUMN rego_code TEXT,
    ADD COLUMN blob_container VARCHAR(255) DEFAULT 'rego-policies',
    ADD COLUMN blob_path VARCHAR(500),
    ADD COLUMN blob_url VARCHAR(1000),
    ADD COLUMN rego_template_id VARCHAR(100),
    ADD COLUMN opa_sync_status VARCHAR(50) DEFAULT 'pending',
    ADD COLUMN last_rego_generation TIMESTAMP WITH TIME ZONE,
    ADD COLUMN rego_generation_error TEXT,
    ADD COLUMN rego_version INTEGER DEFAULT 1;

-- Add policy cloning columns from 20250716_06__policy_cloning_enhancements.sql
ALTER TABLE policies ADD COLUMN IF NOT EXISTS original_policy_id UUID REFERENCES policies(policy_id);
ALTER TABLE policies ADD COLUMN IF NOT EXISTS cloned_from_policy_name VARCHAR(255);

-- Add deleted_at for soft delete (from versioning)
ALTER TABLE policies ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Add guardrail_id from 20250811_03_policies_add_guardrail_id.sql
ALTER TABLE policies ADD COLUMN IF NOT EXISTS guardrail_id uuid;

-- Create policy violations table
CREATE TABLE IF NOT EXISTS policy_violations (
    violation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    policy_id UUID REFERENCES policies(policy_id),
    user_id UUID REFERENCES users(user_id),
    violation_type VARCHAR(100) NOT NULL,
    details JSONB,
    severity VARCHAR(20),
    resolved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES users(user_id)
);

-- Create documents table
CREATE TABLE IF NOT EXISTS documents (
    document_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(500) NOT NULL,
    content TEXT,
    document_type VARCHAR(100),
    metadata JSONB,
    file_path VARCHAR(1000),
    is_sensitive BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    updated_by UUID REFERENCES users(user_id)
);

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit_log (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Enhance audit_log table for HIPAA compliance (from 20250716_06__policy_cloning_enhancements.sql)
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS session_id VARCHAR(255);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS request_id VARCHAR(255);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS user_role VARCHAR(50);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS resource_name VARCHAR(255);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS access_level VARCHAR(50);
ALTER TABLE audit_log ADD COLUMN IF NOT EXISTS data_classification VARCHAR(50);

-- ==============================================================================
-- ENHANCED TABLES (from enhanced-database-schema.sql)
-- ==============================================================================

-- MCP Chat Sessions Table
CREATE TABLE IF NOT EXISTS mcp_chat_sessions (
    session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(user_id),
    status VARCHAR(20) DEFAULT 'active', -- active, completed, terminated
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

-- MCP Flow Steps Table (tracks the 12-step process)
CREATE TABLE IF NOT EXISTS mcp_flow_steps (
    step_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id) ON DELETE CASCADE,
    step_number INTEGER NOT NULL, -- 1-12 as per the flow
    step_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    input_data JSONB,
    output_data JSONB,
    processing_time_ms INTEGER,
    policies_applied UUID[] DEFAULT '{}', -- Array of policy IDs applied
    violations_detected UUID[] DEFAULT '{}', -- Array of violation IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Chat Messages Table
CREATE TABLE IF NOT EXISTS chat_messages (
    message_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL, -- user, assistant, system
    content TEXT NOT NULL,
    original_content TEXT, -- before policy filtering
    policies_applied UUID[] DEFAULT '{}',
    is_filtered BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Policy Executions Table (detailed logging)
CREATE TABLE IF NOT EXISTS policy_executions (
    execution_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id) ON DELETE CASCADE,
    step_id UUID REFERENCES mcp_flow_steps(step_id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id),
    execution_status VARCHAR(20), -- passed, failed, error
    input_data JSONB,
    output_data JSONB,
    execution_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Azure OpenAI API Calls Table (for monitoring and billing)
CREATE TABLE IF NOT EXISTS openai_api_calls (
    call_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES mcp_chat_sessions(session_id) ON DELETE CASCADE,
    step_id UUID REFERENCES mcp_flow_steps(step_id) ON DELETE CASCADE,
    model_name VARCHAR(50),
    prompt_tokens INTEGER,
    completion_tokens INTEGER,
    total_tokens INTEGER,
    cost_estimate DECIMAL(10,6),
    response_time_ms INTEGER,
    status_code INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Policy Templates Table (for easier policy creation)
CREATE TABLE IF NOT EXISTS policy_templates (
    template_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    template_definition JSONB NOT NULL,
    is_system_template BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    guardrail_id VARCHAR(255)
);

-- System Metrics Table (for dashboard analytics)
CREATE TABLE IF NOT EXISTS system_metrics (
    metric_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(100) NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    metric_value NUMERIC(15,4),
    dimensions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==============================================================================
-- AGENTS AND REGO TEMPLATES (from 20250716_02__create_agents_rego_templates.sql)
-- ==============================================================================

CREATE TABLE IF NOT EXISTS agents (
    agent_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    agent_type VARCHAR(100) DEFAULT 'policy_engine',
    endpoint_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    configuration JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- Add extensions from 20250806_04_extend_core_entities.sql
ALTER TABLE agents
    ADD COLUMN IF NOT EXISTS vendor VARCHAR(255),
    ADD COLUMN IF NOT EXISTS department VARCHAR(255),
    ADD COLUMN IF NOT EXISTS risk_score NUMERIC(4,2) DEFAULT 0.0,
    ADD COLUMN IF NOT EXISTS status agent_status_enum DEFAULT 'active';

-- Add soft delete from 20250806_05_soft_delete_columns.sql
ALTER TABLE agents ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

CREATE TABLE IF NOT EXISTS rego_templates (
    template_id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    policy_category VARCHAR(100) NOT NULL,
    template_content TEXT NOT NULL,
    variables JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- ==============================================================================
-- ROLES AND USER MANAGEMENT (from 20250806_01_roles_privileges.sql)
-- ==============================================================================

-- Roles catalog
CREATE TABLE IF NOT EXISTS roles (
    role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(100) UNIQUE NOT NULL,   -- e.g. ADMIN, CISO
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id),
    permissions TEXT[] DEFAULT '{}'
);

-- User ↔ Role mapping
CREATE TABLE IF NOT EXISTS user_roles (
    user_id UUID REFERENCES users(user_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, role_id)
);

-- ==============================================================================
-- POLICY GROUPS (from 20250806_02_policy_groups.sql)
-- ==============================================================================

-- Table: policy_groups
CREATE TABLE IF NOT EXISTS policy_groups (
    group_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_template BOOLEAN DEFAULT false,
    severity severity_level_enum DEFAULT 'medium',
    status lifecycle_status_enum DEFAULT 'active',
    version VARCHAR(20) DEFAULT 'v1.0.0',
    tags TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(user_id)
);

-- Add soft delete from 20250806_05_soft_delete_columns.sql
ALTER TABLE policy_groups ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;

-- Linking table: policy_group_policies
CREATE TABLE IF NOT EXISTS policy_group_policies (
    group_id UUID REFERENCES policy_groups(group_id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    PRIMARY KEY (group_id, policy_id)
);

-- ==============================================================================
-- AGENT ACCESS CONTROL (from 20250806_03_agent_access.sql)
-- ==============================================================================

-- ACL table: agent_access
CREATE TABLE IF NOT EXISTS agent_access (
    agent_id UUID REFERENCES agents(agent_id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(role_id) ON DELETE CASCADE,
    access_level access_level_enum DEFAULT 'view',
    PRIMARY KEY (agent_id, role_id)
);

-- Link table: agent_policies
CREATE TABLE IF NOT EXISTS agent_policies (
    agent_id UUID REFERENCES agents(agent_id) ON DELETE CASCADE,
    policy_id UUID REFERENCES policies(policy_id) ON DELETE CASCADE,
    link_type link_type_enum NOT NULL,
    PRIMARY KEY (agent_id, policy_id)
);

-- ==============================================================================
-- AGENT ROLE POLICIES (from 20250811_01_agent_role_policies.sql)
-- ==============================================================================

-- Table to map an agent's role to a specific policy via a policy group
CREATE TABLE IF NOT EXISTS agent_role_policies (
  agent_id  UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
  role_id   UUID NOT NULL REFERENCES roles(role_id)  ON DELETE CASCADE,
  group_id  UUID NOT NULL REFERENCES policy_groups(group_id) ON DELETE CASCADE,
  policy_id UUID NOT NULL REFERENCES policies(policy_id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (agent_id, role_id, group_id, policy_id)
);

-- Ensure the (group_id, policy_id) pair is valid according to policy_group_policies
ALTER TABLE agent_role_policies
  ADD CONSTRAINT fk_group_policy_pair
  FOREIGN KEY (group_id, policy_id)
  REFERENCES policy_group_policies (group_id, policy_id)
  ON DELETE CASCADE;

-- ==============================================================================
-- ENUM MANAGEMENT (from 20250716_07__enum_management.sql)
-- ==============================================================================

-- Create enum categories table
CREATE TABLE IF NOT EXISTS enum_categories (
    category_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    policy_type VARCHAR(50) NOT NULL,
    field_path VARCHAR(200) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(policy_type, field_path)
);

-- Create enum values table
CREATE TABLE IF NOT EXISTS enum_values (
    value_id SERIAL PRIMARY KEY,
    category_id INTEGER REFERENCES enum_categories(category_id) ON DELETE CASCADE,
    value VARCHAR(200) NOT NULL,
    display_name VARCHAR(200),
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==============================================================================
-- INTEGRATION TABLES (from 20250812_01_external_integrations.sql)
-- ==============================================================================

-- Outbox and DLQ tables
CREATE TABLE IF NOT EXISTS integration_outbox (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID,
  destination TEXT NOT NULL DEFAULT 'webhook', -- future: 'bus'
  event_type TEXT NOT NULL,
  event_version INTEGER NOT NULL DEFAULT 1,
  payload_json JSONB NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending', -- pending|delivered|failed|dead
  attempts INTEGER NOT NULL DEFAULT 0,
  next_attempt_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  last_error TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS integration_dlq (
  id UUID PRIMARY KEY,
  tenant_id UUID,
  destination TEXT NOT NULL,
  event_type TEXT NOT NULL,
  event_version INTEGER NOT NULL,
  payload_json JSONB NOT NULL,
  attempts INTEGER NOT NULL,
  last_error TEXT,
  dead_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
);

-- Additional tables for advanced features
CREATE TABLE IF NOT EXISTS guardrail_services (
    id uuid PRIMARY KEY,
    service_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50) DEFAULT '1.0.0' NOT NULL,
    type VARCHAR(50) DEFAULT 'modifier' NOT NULL,
    endpoint VARCHAR(500) NOT NULL,
    health_check_path VARCHAR(255) DEFAULT '/health',
    timeout_ms INTEGER DEFAULT 100,
    capabilities VARCHAR[] DEFAULT '{}',
    supported_content_types VARCHAR[] DEFAULT '{text/plain,application/json}',
    status VARCHAR(50) DEFAULT 'unknown',
    last_health_check TIMESTAMPTZ,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    UNIQUE(service_id, version)
);

CREATE TABLE IF NOT EXISTS alembic_version (
    version_num VARCHAR(32) PRIMARY KEY
);

-- ==============================================================================
-- INDEXES
-- ==============================================================================

-- Base table indexes
CREATE INDEX IF NOT EXISTS idx_users_azure_ad_id ON users(azure_ad_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_policies_active ON policies(is_active);
CREATE INDEX IF NOT EXISTS idx_policies_category ON policies(category);
CREATE INDEX IF NOT EXISTS idx_policies_severity ON policies(severity);
CREATE INDEX IF NOT EXISTS idx_policies_roles ON policies USING GIN(applies_to_roles);
CREATE INDEX IF NOT EXISTS idx_policies_rego_status ON policies(opa_sync_status);
CREATE INDEX IF NOT EXISTS idx_policies_last_generation ON policies(last_rego_generation);
CREATE INDEX IF NOT EXISTS idx_policies_blob_path ON policies(blob_path);
CREATE INDEX IF NOT EXISTS idx_policies_original_id ON policies(original_policy_id);
CREATE INDEX IF NOT EXISTS idx_violations_policy_id ON policy_violations(policy_id);
CREATE INDEX IF NOT EXISTS idx_violations_user_id ON policy_violations(user_id);
CREATE INDEX IF NOT EXISTS idx_violations_resolved ON policy_violations(resolved);
CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(document_type);
CREATE INDEX IF NOT EXISTS idx_documents_sensitive ON documents(is_sensitive);
CREATE INDEX IF NOT EXISTS idx_audit_user_id ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action);
CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_log(resource_type, resource_id);

-- HIPAA audit indexes (from 20250716_06__policy_cloning_enhancements.sql)
CREATE INDEX IF NOT EXISTS idx_audit_log_session_id ON audit_log(session_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_request_id ON audit_log(request_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_role ON audit_log(user_role);

-- Enhanced table indexes
CREATE INDEX IF NOT EXISTS idx_mcp_chat_sessions_user ON mcp_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_mcp_flow_steps_session ON mcp_flow_steps(session_id);
CREATE UNIQUE INDEX idx_mcp_flow_steps_session_step ON mcp_flow_steps(session_id, step_number);
CREATE INDEX IF NOT EXISTS idx_chat_messages_session ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_policy_executions_session ON policy_executions(session_id);
CREATE INDEX IF NOT EXISTS idx_policy_executions_policy ON policy_executions(policy_id);
CREATE INDEX IF NOT EXISTS idx_openai_api_calls_session ON openai_api_calls(session_id);
CREATE INDEX IF NOT EXISTS idx_system_metrics_type_name ON system_metrics(metric_type, metric_name);

-- Agents and rego templates indexes
CREATE INDEX IF NOT EXISTS idx_agents_active ON agents(is_active);
CREATE INDEX IF NOT EXISTS idx_agents_type ON agents(agent_type);

-- Policy groups indexes
CREATE INDEX IF NOT EXISTS idx_policy_groups_status ON policy_groups(status);
CREATE INDEX IF NOT EXISTS idx_pg_policies_policy ON policy_group_policies(policy_id);

-- Agent access indexes
CREATE INDEX IF NOT EXISTS idx_agent_access_role ON agent_access(role_id);
CREATE INDEX IF NOT EXISTS idx_agent_policies_agent ON agent_policies(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_policies_policy ON agent_policies(policy_id);

-- Agent role policies indexes (from 20250811_02_agent_role_policies_indexes.sql)
CREATE INDEX IF NOT EXISTS idx_arp_agent ON agent_role_policies(agent_id);
CREATE INDEX IF NOT EXISTS idx_arp_agent_role ON agent_role_policies(agent_id, role_id);
CREATE INDEX IF NOT EXISTS idx_arp_group ON agent_role_policies(group_id);
CREATE INDEX IF NOT EXISTS idx_arp_policy ON agent_role_policies(policy_id);

-- Integration indexes (exact names from original migration)
CREATE INDEX IF NOT EXISTS idx_integration_outbox_status ON integration_outbox(status);
CREATE INDEX IF NOT EXISTS idx_integration_outbox_next_attempt ON integration_outbox(next_attempt_at);
CREATE INDEX IF NOT EXISTS idx_integration_outbox_status_next ON integration_outbox(status, next_attempt_at);
CREATE INDEX IF NOT EXISTS idx_integration_outbox_created_at ON integration_outbox(created_at);

-- ==============================================================================
-- FUNCTIONS
-- ==============================================================================

-- Function for logging Rego operations (from 20250716_04__audit_log_enhancements.sql)
CREATE OR REPLACE FUNCTION log_rego_operation(
    user_uuid UUID,
    operation_details JSONB
) RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_log (user_id, action, resource_type, resource_id, new_values, timestamp)
    VALUES (user_uuid, 'policy_rego', 'policy_rego', operation_details->>'policy_id', operation_details, CURRENT_TIMESTAMP);
END;
$$ LANGUAGE plpgsql;

-- Function to generate Rego code for a policy (from 20250716_05__rego_generation_functions.sql)
CREATE OR REPLACE FUNCTION generate_rego_for_policy(policy_uuid UUID)
RETURNS JSON AS $$
DECLARE
    policy_record policies%ROWTYPE;
    template_record rego_templates%ROWTYPE;
    generated_rego TEXT;
    blob_path_var VARCHAR(500);
    result JSON;
BEGIN
    -- Get policy details
    SELECT * INTO policy_record FROM policies WHERE policy_id = policy_uuid;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Policy not found');
    END IF;
    -- Get template based on policy category
    SELECT * INTO template_record FROM rego_templates WHERE policy_category = policy_record.category AND is_active = true LIMIT 1;
    IF NOT FOUND THEN
        RETURN json_build_object('success', false, 'error', 'Template not found');
    END IF;
    -- Increment version before generating Rego
    UPDATE policies SET version = version + 1 WHERE policy_id = policy_uuid RETURNING * INTO policy_record;
    -- Simulate template rendering (actual implementation should use server-side code)
    generated_rego := template_record.template_content;
    blob_path_var := CONCAT('active/policy_', policy_record.policy_id, '_', policy_record.category, '_v', policy_record.version, '.rego');
    -- Update policy with generated Rego
    UPDATE policies SET rego_code = generated_rego, blob_path = blob_path_var, rego_template_id = template_record.template_id, last_rego_generation = CURRENT_TIMESTAMP, opa_sync_status = 'generated', rego_version = policy_record.version WHERE policy_id = policy_uuid;
    -- Log operation
    --PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'generate_rego', 'status', 'success'));
    RETURN json_build_object('success', true, 'rego_preview', generated_rego, 'blob_path', blob_path_var);
END;
$$ LANGUAGE plpgsql;

-- Function to rollback Rego generation for a policy (from 20250716_05__rego_generation_functions.sql)
CREATE OR REPLACE FUNCTION rollback_rego_generation(policy_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE policies SET rego_code = NULL, blob_path = NULL, rego_template_id = NULL, last_rego_generation = NULL, opa_sync_status = 'pending', rego_generation_error = NULL WHERE policy_id = policy_uuid;
    PERFORM log_rego_operation('00000000-0000-0000-0000-000000000000', json_build_object('policy_id', policy_uuid, 'action', 'rollback_rego', 'status', 'success'));
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Enhanced audit logging function (from 20250716_06__policy_cloning_enhancements.sql)
CREATE OR REPLACE FUNCTION log_hipaa_audit_event(
    p_user_id UUID,
    p_action VARCHAR(100),
    p_resource_type VARCHAR(100),
    p_resource_id UUID,
    p_old_values JSONB,
    p_new_values JSONB,
    p_session_id VARCHAR(255),
    p_request_id VARCHAR(255),
    p_user_role VARCHAR(50),
    p_resource_name VARCHAR(255),
    p_access_level VARCHAR(50),
    p_data_classification VARCHAR(50)
) RETURNS VOID AS $$
BEGIN
    INSERT INTO audit_log (
        user_id, action, resource_type, resource_id, old_values, new_values,
        ip_address, user_agent, timestamp, session_id, request_id, user_role,
        resource_name, access_level, data_classification
    ) VALUES (
        p_user_id, p_action, p_resource_type, p_resource_id, p_old_values, p_new_values,
        inet_client_addr(), current_setting('application_name', true), CURRENT_TIMESTAMP,
        p_session_id, p_request_id, p_user_role, p_resource_name, p_access_level, p_data_classification
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get policy templates by category (from 20250716_06__policy_cloning_enhancements.sql)
CREATE OR REPLACE FUNCTION get_policy_template_by_category(p_category VARCHAR(100))
RETURNS TABLE(template_id UUID, name VARCHAR(255), description TEXT, template_definition JSONB) AS $$
BEGIN
    RETURN QUERY
    SELECT pt.template_id, pt.name, pt.description, pt.template_definition
    FROM policy_templates pt
    WHERE pt.category = p_category
    AND pt.is_system_template = true
    ORDER BY pt.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to clone a policy (from 20250716_06__policy_cloning_enhancements.sql)
CREATE OR REPLACE FUNCTION clone_policy(
    p_original_policy_id UUID,
    p_new_name VARCHAR(255),
    p_new_description TEXT,
    p_cloned_by_user_id UUID
) RETURNS UUID AS $$
DECLARE
    v_new_policy_id UUID;
    v_original_policy RECORD;
BEGIN
    -- Get original policy data
    SELECT * INTO v_original_policy
    FROM policies
    WHERE policy_id = p_original_policy_id
    AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Original policy not found or deleted';
    END IF;
    
    -- Create new policy as clone
    INSERT INTO policies (
        name, description, category, policy_type, definition, version,
        is_active, severity, applies_to_roles, created_by, updated_by,
        original_policy_id, cloned_from_policy_name
    ) VALUES (
        p_new_name, p_new_description, v_original_policy.category,
        v_original_policy.policy_type, v_original_policy.definition, 1,
        false, v_original_policy.severity, v_original_policy.applies_to_roles,
        p_cloned_by_user_id, p_cloned_by_user_id,
        p_original_policy_id, v_original_policy.name
    ) RETURNING policy_id INTO v_new_policy_id;
    
    -- Log the cloning event
    PERFORM log_hipaa_audit_event(
        p_cloned_by_user_id,
        'POLICY_CLONED',
        'policy',
        v_new_policy_id,
        jsonb_build_object('original_policy_id', p_original_policy_id, 'original_policy_name', v_original_policy.name),
        jsonb_build_object('new_policy_name', p_new_name, 'new_policy_id', v_new_policy_id),
        NULL, NULL, 'admin', 'policy_cloning', 'write', 'sensitive'
    );
    
    RETURN v_new_policy_id;
END;
$$ LANGUAGE plpgsql;

-- Function to search policies with pagination (from 20250716_06__policy_cloning_enhancements.sql)
CREATE OR REPLACE FUNCTION search_policies(
    p_search_term VARCHAR(255),
    p_category VARCHAR(100),
    p_severity VARCHAR(20),
    p_is_active BOOLEAN,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    is_active BOOLEAN,
    policy_type VARCHAR(50),
    definition JSONB,
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH filtered_policies AS (
        SELECT p.*,
               COUNT(*) OVER() as total_count
        FROM policies p
        WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    )
    SELECT fp.policy_id, fp.name, fp.description, fp.category, fp.severity,
           fp.is_active, fp.policy_type, fp.definition, fp.applies_to_roles,
           fp.created_at, fp.updated_at, fp.total_count
    FROM filtered_policies fp
    ORDER BY fp.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- Enum management functions (from 20250716_07__enum_management.sql)
CREATE OR REPLACE FUNCTION get_enum_values(p_policy_type VARCHAR(50), p_field_path VARCHAR(200))
RETURNS TABLE(value VARCHAR(200), display_name VARCHAR(200), description TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT ev.value, ev.display_name, ev.description
    FROM enum_values ev
    JOIN enum_categories ec ON ev.category_id = ec.category_id
    WHERE ec.policy_type = p_policy_type 
    AND ec.field_path = p_field_path
    AND ec.is_active = true 
    AND ev.is_active = true
    ORDER BY ev.sort_order, ev.value;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_enum_fields_for_policy_type(p_policy_type VARCHAR(50))
RETURNS TABLE(field_path VARCHAR(200), category_name VARCHAR(100), description TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT ec.field_path, ec.name, ec.description
    FROM enum_categories ec
    WHERE ec.policy_type = p_policy_type
    AND ec.is_active = true
    ORDER BY ec.name;
END;
$$ LANGUAGE plpgsql;

-- Integration helper function (from 20250812_01_external_integrations.sql)
CREATE OR REPLACE FUNCTION integration_enqueue_event(p_event_type TEXT, p_payload JSONB)
RETURNS VOID AS $$
BEGIN
  INSERT INTO integration_outbox (event_type, payload_json, status, attempts, next_attempt_at)
  VALUES (p_event_type, p_payload, 'pending', 0, CURRENT_TIMESTAMP);
END;
$$ LANGUAGE plpgsql;

-- Enum trigger functions (from 20250716_07__enum_management.sql)
CREATE OR REPLACE FUNCTION update_enum_categories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_enum_values_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Integration trigger functions (from 20250812_01_external_integrations.sql)
CREATE OR REPLACE FUNCTION trg_agents_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'agent.created';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','created')
    );
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'agent.updated';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', NEW.agent_id),
      'data', jsonb_build_object('change_type','updated')
    );
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'agent.deleted';
    v_payload := jsonb_build_object(
      'subject', jsonb_build_object('resource_type','agent','resource_id', OLD.agent_id),
      'data', jsonb_build_object('change_type','deleted')
    );
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION trg_roles_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'role.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'role.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', NEW.role_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'role.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','role','resource_id', OLD.role_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION trg_policies_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'policy.created';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'UPDATE' THEN
    v_event := 'policy.updated';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', NEW.policy_id), 'data', jsonb_build_object('change_type','updated'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'policy.deleted';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','policy','resource_id', OLD.policy_id), 'data', jsonb_build_object('change_type','deleted'));
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION trg_arp_enqueue()
RETURNS TRIGGER AS $$
DECLARE
  v_event TEXT;
  v_payload JSONB;
BEGIN
  IF TG_OP = 'INSERT' THEN
    v_event := 'assignment.linked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', NEW.agent_id, 'role_id', NEW.role_id, 'group_id', NEW.group_id, 'policy_id', NEW.policy_id)), 'data', jsonb_build_object('change_type','created'));
  ELSIF TG_OP = 'DELETE' THEN
    v_event := 'assignment.unlinked';
    v_payload := jsonb_build_object('subject', jsonb_build_object('resource_type','agent_role_policy','resource_id', jsonb_build_object('agent_id', OLD.agent_id, 'role_id', OLD.role_id, 'group_id', OLD.group_id, 'policy_id', OLD.policy_id)), 'data', jsonb_build_object('change_type','deleted'));
  ELSE
    RETURN COALESCE(NEW, OLD);
  END IF;
  PERFORM integration_enqueue_event(v_event, v_payload);
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- TRIGGERS
-- ==============================================================================

-- Apply updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_policies_updated_at BEFORE UPDATE ON policies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_mcp_chat_sessions_updated_at BEFORE UPDATE ON mcp_chat_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rego_templates_updated_at BEFORE UPDATE ON rego_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger to keep policy_groups updated_at current
CREATE TRIGGER update_policy_groups_updated_at
    BEFORE UPDATE ON policy_groups
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enum triggers (exact names from original migration)
CREATE TRIGGER update_enum_categories_updated_at_trigger
    BEFORE UPDATE ON enum_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_enum_categories_updated_at();

CREATE TRIGGER update_enum_values_updated_at_trigger
    BEFORE UPDATE ON enum_values
    FOR EACH ROW
    EXECUTE FUNCTION update_enum_values_updated_at();

-- Integration triggers
CREATE TRIGGER trg_integration_agents
    AFTER INSERT OR UPDATE OR DELETE ON agents
    FOR EACH ROW EXECUTE FUNCTION trg_agents_enqueue();

CREATE TRIGGER trg_roles_integration
    AFTER INSERT OR UPDATE OR DELETE ON roles
    FOR EACH ROW EXECUTE FUNCTION trg_roles_enqueue();

CREATE TRIGGER trg_policies_integration
    AFTER INSERT OR UPDATE OR DELETE ON policies
    FOR EACH ROW EXECUTE FUNCTION trg_policies_enqueue();

CREATE TRIGGER trg_integration_arp
    AFTER INSERT OR DELETE ON agent_role_policies
    FOR EACH ROW EXECUTE FUNCTION trg_arp_enqueue();

-- Outbox update trigger
CREATE TRIGGER trg_outbox_updated_at BEFORE UPDATE ON integration_outbox
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;

-- ==============================================================================
-- POST-CREATION DATA SETUP
-- ==============================================================================

BEGIN;

-- Insert system user (for policies, etc.)
INSERT INTO users (user_id, azure_ad_id, email, first_name, last_name, role)
VALUES ('00000000-0000-0000-0000-000000000000', 'system', '<EMAIL>', 'System', 'User', 'system')
ON CONFLICT (user_id) DO NOTHING;

-- Update existing policies to ensure they have proper audit trail (from 20250716_06)
UPDATE policies 
SET updated_at = CURRENT_TIMESTAMP 
WHERE updated_at IS NULL;

-- Insert initial enum categories and values (from 20250716_07__enum_management.sql)
INSERT INTO enum_categories (name, description, policy_type, field_path) VALUES
('Medical Roles', 'Roles that can access medical data', 'medical_privacy', 'allowed_roles'),
('Medical Fields', 'Medical fields requiring special protection', 'medical_privacy', 'protected_fields'),
('Data Privacy Roles', 'Roles that can access sensitive data', 'data_privacy', 'allowed_roles'),
('Data Privacy Fields', 'Data fields requiring privacy protection', 'data_privacy', 'protected_fields'),
('Access Control Roles', 'Roles for access control policies', 'access_control', 'allowed_roles'),
('Compliance Roles', 'Roles for compliance policies', 'compliance', 'allowed_roles'),
('Severity Levels', 'Policy severity levels', 'all', 'severity');

-- Insert enum values for each category
INSERT INTO enum_values (category_id, value, display_name, description, sort_order) VALUES
-- Medical Roles
(1, 'doctor', 'Doctor', 'Medical doctors with full access', 1),
(1, 'nurse', 'Nurse', 'Nursing staff with patient care access', 2),
(1, 'admin', 'Administrator', 'System administrators', 3),
(1, 'pharmacist', 'Pharmacist', 'Pharmacy staff with medication access', 4),
(1, 'lab_tech', 'Lab Technician', 'Laboratory technicians', 5),
(1, 'specialist', 'Specialist', 'Medical specialists', 6),
(1, 'resident', 'Resident', 'Medical residents', 7),

-- Medical Fields
(2, 'diagnosis', 'Diagnosis', 'Patient diagnosis information', 1),
(2, 'medication', 'Medication', 'Prescribed medications', 2),
(2, 'lab_orders', 'Lab Orders', 'Laboratory test orders', 3),
(2, 'medical_record_number', 'Medical Record Number', 'Patient record identifier', 4),
(2, 'treatment_plan', 'Treatment Plan', 'Patient treatment plans', 5),
(2, 'billing_info', 'Billing Information', 'Medical billing data', 6),
(2, 'patient_notes', 'Patient Notes', 'Clinical notes and observations', 7),
(2, 'prescriptions', 'Prescriptions', 'Medication prescriptions', 8),
(2, 'vital_signs', 'Vital Signs', 'Patient vital signs', 9),
(2, 'allergies', 'Allergies', 'Patient allergy information', 10),
(2, 'family_history', 'Family History', 'Patient family medical history', 11),
(2, 'immunizations', 'Immunizations', 'Patient immunization records', 12),

-- Data Privacy Roles
(3, 'admin', 'Administrator', 'System administrators', 1),
(3, 'manager', 'Manager', 'Department managers', 2),
(3, 'analyst', 'Analyst', 'Data analysts', 3),
(3, 'user', 'User', 'General users', 4),
(3, 'viewer', 'Viewer', 'Read-only users', 5),
(3, 'editor', 'Editor', 'Content editors', 6),

-- Data Privacy Fields
(4, 'personal_info', 'Personal Information', 'Personal identification data', 1),
(4, 'contact_info', 'Contact Information', 'Contact details', 2),
(4, 'financial_data', 'Financial Data', 'Financial information', 3),
(4, 'employment_data', 'Employment Data', 'Employment information', 4),
(4, 'health_data', 'Health Data', 'Health-related information', 5),

-- Access Control Roles
(5, 'admin', 'Administrator', 'System administrators', 1),
(5, 'manager', 'Manager', 'Department managers', 2),
(5, 'user', 'User', 'General users', 3),
(5, 'guest', 'Guest', 'Temporary users', 4),

-- Compliance Roles
(6, 'compliance_officer', 'Compliance Officer', 'Compliance monitoring staff', 1),
(6, 'auditor', 'Auditor', 'Internal auditors', 2),
(6, 'legal', 'Legal', 'Legal department staff', 3),
(6, 'admin', 'Administrator', 'System administrators', 4),

-- Severity Levels
(7, 'low', 'Low', 'Low priority issues', 1),
(7, 'medium', 'Medium', 'Medium priority issues', 2),
(7, 'high', 'High', 'High priority issues', 3),
(7, 'critical', 'Critical', 'Critical priority issues', 4);

-- Insert audit event for schema creation
INSERT INTO audit_log (user_id, action, resource_type, resource_id, new_values)
VALUES (
    '00000000-0000-0000-0000-000000000000',
    'schema_creation',
    'database',
    null,
    '{"update": "Complete database schema created from consolidated migration files"}'
);

-- ==============================================================================
-- ADDITIONAL UNIQUE CONSTRAINTS (needed for demo data ON CONFLICT clauses)
-- ==============================================================================

-- Add missing unique constraints that the demo data expects
DO $$ 
BEGIN
    -- users table - add unique constraint on email
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'users_email_unique') THEN
        ALTER TABLE users ADD CONSTRAINT users_email_unique UNIQUE (email);
    END IF;
    
    -- agents table - add unique constraint on name
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'agents_name_unique') THEN
        ALTER TABLE agents ADD CONSTRAINT agents_name_unique UNIQUE (name);
    END IF;
    
    -- policy_groups table - add unique constraint on name
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'policy_groups_name_unique') THEN
        ALTER TABLE policy_groups ADD CONSTRAINT policy_groups_name_unique UNIQUE (name);
    END IF;
    
    -- user_roles table - add unique constraint on (user_id, role_id)
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'user_roles_user_role_unique') THEN
        ALTER TABLE user_roles ADD CONSTRAINT user_roles_user_role_unique UNIQUE (user_id, role_id);
    END IF;
    
    -- policy_group_policies table - add unique constraint on (group_id, policy_id)
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'policy_group_policies_unique') THEN
        ALTER TABLE policy_group_policies ADD CONSTRAINT policy_group_policies_unique UNIQUE (group_id, policy_id);
    END IF;
    
    -- agent_policies table - add unique constraint on (agent_id, policy_id, link_type)
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'agent_policies_unique') THEN
        ALTER TABLE agent_policies ADD CONSTRAINT agent_policies_unique UNIQUE (agent_id, policy_id, link_type);
    END IF;
    
    -- agent_access table - add unique constraint on (agent_id, role_id)
    IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'agent_access_unique') THEN
        ALTER TABLE agent_access ADD CONSTRAINT agent_access_unique UNIQUE (agent_id, role_id);
    END IF;
END $$;

COMMIT;

-- ==============================================================================
-- END OF CONSOLIDATED SCHEMA
-- ==============================================================================

\echo 'Vitea.ai Policy Management System consolidated schema created successfully!'
\echo 'Total tables created: 27'
\echo 'Total functions created: 14'
\echo 'Total triggers created: 12'
\echo 'Total enums created: 6'
\echo ''
\echo 'Schema consolidated from original migration files:'
\echo '- Base schema and enhanced tables'
\echo '- All agent, role, and policy management features'
\echo '- Complete audit logging and HIPAA compliance'
\echo '- External integration support'
\echo '- Enum management system'
\echo ''
-- ==============================================================================
-- TESTING AND EVALUATION TABLES (from scripts/create-complete-schema.sql)
-- ==============================================================================

-- Datasets table for storing test datasets
CREATE TABLE IF NOT EXISTS datasets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL DEFAULT 'custom',
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    data JSONB NOT NULL DEFAULT '[]'::jsonb,
    record_count INTEGER NOT NULL DEFAULT 0,
    created_by VARCHAR(100) NOT NULL DEFAULT 'system',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Dataset entries table for individual test cases
CREATE TABLE IF NOT EXISTS dataset_entries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dataset_id UUID REFERENCES datasets(id) ON DELETE CASCADE,
    test_case_type VARCHAR(20) NOT NULL,
    input JSONB NOT NULL,
    expected_output TEXT,
    context TEXT,
    retrieval_context TEXT[],
    tools_called TEXT[],
    expected_outcome VARCHAR(50),
    scenario TEXT,
    initial_context TEXT,
    entry_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Evaluation metrics table
CREATE TABLE IF NOT EXISTS evaluation_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    implementation_type VARCHAR(50),
    config JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiments table for test runs
CREATE TABLE IF NOT EXISTS experiments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    dataset_id UUID REFERENCES datasets(id) ON DELETE CASCADE,
    agent_config JSONB,
    execution_mode VARCHAR(20) NOT NULL DEFAULT 'automated',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    progress INTEGER NOT NULL DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_by VARCHAR(100) NOT NULL DEFAULT 'system',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Evaluations table for storing evaluation results
CREATE TABLE IF NOT EXISTS evaluations (
    id SERIAL PRIMARY KEY,
    evaluation_id VARCHAR(255) NOT NULL,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    experiment_name VARCHAR(255),
    agent_name VARCHAR(255),
    dataset_name VARCHAR(255),
    evaluation_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'pending',
    evaluations JSONB,
    summary JSONB,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITHOUT TIME ZONE
);

-- Test results table for individual test case results
CREATE TABLE IF NOT EXISTS test_results (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    experiment_id UUID REFERENCES experiments(id) ON DELETE CASCADE,
    dataset_entry_id UUID REFERENCES dataset_entries(id) ON DELETE CASCADE,
    test_case_type VARCHAR(20) NOT NULL,
    input JSONB NOT NULL,
    expected_output TEXT,
    actual_output TEXT NOT NULL,
    context TEXT,
    retrieval_context TEXT[],
    tools_called TEXT[],
    expected_outcome VARCHAR(50),
    scenario TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    score DECIMAL(5,2),
    latency_ms INTEGER,
    token_count INTEGER,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Experiment evaluations junction table
CREATE TABLE IF NOT EXISTS experiment_evaluations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    experiment_id UUID NOT NULL REFERENCES experiments(id) ON DELETE CASCADE,
    metric_id UUID NOT NULL REFERENCES evaluation_metrics(id),
    status VARCHAR(20) DEFAULT 'pending',
    score DECIMAL(5,2),
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(experiment_id, metric_id)
);

-- ==============================================================================
-- TESTING AND EVALUATION INDEXES
-- ==============================================================================

CREATE INDEX IF NOT EXISTS idx_datasets_status ON datasets(status);
CREATE INDEX IF NOT EXISTS idx_datasets_created_at ON datasets(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_dataset_entries_dataset ON dataset_entries(dataset_id);
CREATE INDEX IF NOT EXISTS idx_experiments_dataset ON experiments(dataset_id);
CREATE INDEX IF NOT EXISTS idx_experiments_status ON experiments(status);
CREATE INDEX IF NOT EXISTS idx_test_results_experiment ON test_results(experiment_id);
CREATE INDEX IF NOT EXISTS idx_test_results_status ON test_results(status);
CREATE INDEX IF NOT EXISTS idx_evaluations_experiment ON evaluations(experiment_id);
CREATE INDEX IF NOT EXISTS idx_experiment_evaluations_experiment ON experiment_evaluations(experiment_id);

-- ==============================================================================
-- UPDATED SEARCH FUNCTION (from configs/fix_search_policies_function.sql)
-- ==============================================================================

-- Drop the existing search_policies function first
DROP FUNCTION IF EXISTS search_policies(VARCHAR(255), VARCHAR(100), VARCHAR(20), BOOLEAN, INTEGER, INTEGER);

-- Create the updated search_policies function with definition and other missing columns
CREATE OR REPLACE FUNCTION search_policies(
    p_search_term VARCHAR(255),
    p_category VARCHAR(100),
    p_severity VARCHAR(20),
    p_is_active BOOLEAN,
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
    policy_id UUID,
    name VARCHAR(255),
    description TEXT,
    category VARCHAR(100),
    severity VARCHAR(20),
    is_active BOOLEAN,
    policy_type VARCHAR(50),
    definition JSONB,
    applies_to_roles TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH filtered_policies AS (
        SELECT p.*,
               COUNT(*) OVER() as total_count
        FROM policies p
        WHERE p.deleted_at IS NULL
        AND (p_search_term IS NULL OR 
             p.name ILIKE '%' || p_search_term || '%' OR 
             p.description ILIKE '%' || p_search_term || '%')
        AND (p_category IS NULL OR p.category = p_category)
        AND (p_severity IS NULL OR p.severity = p_severity)
        AND (p_is_active IS NULL OR p.is_active = p_is_active)
    )
    SELECT fp.policy_id, fp.name, fp.description, fp.category, fp.severity,
           fp.is_active, fp.policy_type, fp.definition, fp.applies_to_roles,
           fp.created_at, fp.updated_at, fp.total_count
    FROM filtered_policies fp
    ORDER BY fp.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- POLICY TEMPLATES DATA (from configs/policy_templates.sql)
-- ==============================================================================

-- Insert comprehensive policy templates for all categories
INSERT INTO policy_templates (name, description, category, template_definition, is_system_template) VALUES
-- Data Masking Templates
(
    'PII Masking Template',
    'Template for masking personally identifiable information',
    'data_masking',
    '{
        "type": "data_masking",
        "field_patterns": ["ssn", "social_security_number", "phone", "email"],
        "regex_patterns": ["\\\\b\\\\d{3}-\\\\d{2}-\\\\d{4}\\\\b", "\\\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\\\.[A-Z|a-z]{2,}\\\\b"],
        "mask_format": "XXX-XX-{last4}",
        "severity": "critical"
    }',
    true
),
(
    'Phone Number Masking Template',
    'Template for masking phone number fields',
    'data_masking',
    '{
        "type": "data_masking",
        "mask_format": "XXX-XX-{last4}",
        "field_patterns": ["phone", "phone_number"],
        "regex_patterns": ["\\\\b\\\\d{3}-\\\\d{2}-\\\\d{4}\\\\b"],
        "severity": "high"
    }',
    true
),

-- Content Safety Templates
(
    'Content Safety Template',
    'Template for detecting harmful or inappropriate content',
    'content_safety',
    '{
        "type": "content_filtering",
        "blocked_categories": ["hate_speech", "violence", "self_harm", "sexual"],
        "confidence_threshold": 0.7,
        "action": "block_and_log",
        "severity": "high"
    }',
    true
),

-- Medical Privacy Templates
(
    'Medical Info Protection Template',
    'Template for protecting sensitive medical information',
    'medical_privacy',
    '{
        "type": "medical_privacy",
        "protected_fields": ["diagnosis", "medication", "medical_record_number", "treatment_plan"],
        "hipaa_compliance": true,
        "allowed_roles": ["doctor", "nurse", "admin"],
        "severity": "critical"
    }',
    true
),

-- Data Encryption Templates
(
    'Data Encryption Template',
    'Template for encrypting sensitive data fields',
    'data_encryption',
    '{
        "type": "data_encryption",
        "encryption_algorithm": "AES-256",
        "fields_to_encrypt": ["credit_card", "bank_account", "ssn"],
        "key_rotation_days": 90,
        "severity": "critical"
    }',
    true
),

-- Access Logging Templates
(
    'Access Logging Template',
    'Template for comprehensive access logging',
    'access_logging',
    '{
        "type": "access_logging",
        "log_level": "detailed",
        "log_fields": ["user_id", "timestamp", "resource", "action", "ip_address"],
        "retention_days": 2555,
        "hipaa_compliant": true,
        "severity": "high"
    }',
    true
),

-- Data Retention Templates
(
    'Data Retention Template',
    'Template for data retention policies',
    'data_retention',
    '{
        "type": "data_retention",
        "retention_period_days": 2555,
        "data_types": ["patient_records", "medical_history", "billing_info"],
        "auto_deletion": true,
        "backup_retention_days": 365,
        "severity": "critical"
    }',
    true
),

-- Access Control Templates
(
    'Access Control Template',
    'Template for role-based access control',
    'access_control',
    '{
        "type": "access_control",
        "allowed_roles": ["admin", "manager"],
        "restricted_actions": ["delete", "export"],
        "ip_whitelist": ["***********/24"],
        "time_restrictions": {
            "start_time": "09:00",
            "end_time": "17:00",
            "timezone": "UTC"
        },
        "severity": "high"
    }',
    true
),

-- Compliance Templates
(
    'Compliance Template',
    'Template for regulatory compliance policies',
    'compliance',
    '{
        "type": "compliance",
        "regulations": ["HIPAA", "GDPR", "SOX"],
        "audit_requirements": true,
        "documentation_required": true,
        "penalty_severity": "high",
        "severity": "critical"
    }',
    true
) ON CONFLICT (name) DO NOTHING;

COMMIT;

-- ==============================================================================
-- SUMMARY MESSAGE
-- ==============================================================================

\echo 'Vitea.ai Policy Management System consolidated schema created successfully!'
\echo 'Total tables created: 33'
\echo 'Total functions created: 16+'
\echo 'Total triggers created: 12+'
\echo 'Total enums created: 6'
\echo ''
\echo 'Schema consolidated from all migration files:'
\echo '- Base schema and enhanced tables'
\echo '- All agent, role, and policy management features'  
\echo '- Complete audit logging and HIPAA compliance'
\echo '- External integration support'
\echo '- Enum management system'
\echo '- Testing and evaluation framework'
\echo '- Updated search functions'
\echo '- Comprehensive policy templates'
\echo ''
\echo 'Next steps:'
\echo '1. Run ANALYZE to optimize query planning'
\echo '2. Database is ready with sample policy templates'
\echo '3. Review and adjust any environment-specific settings'