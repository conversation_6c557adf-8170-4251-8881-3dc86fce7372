#!/bin/bash
set -e

# =============================================================================
# Pilot Admin Platform Complete Setup Script
# =============================================================================
# Purpose: Initialize Pilot Admin database with complete schema and sample data
# Usage: ./setup-pilot-admin.sh [OPTIONS]
# 
# Options:
#   --container <name>   PostgreSQL container name (default: pilot-postgres)
#   --database <name>    Database name (default: vitea_db)
#   --user <name>        Database user (default: dbadmin)
#   --password <pass>    Database password (default: vitea123)
#   --clean              Drop and recreate database from scratch
#   --schema-only        Only create schema, skip sample data
#   --sample-data        Load HIPAA sample data (default: true)
#   --testing-platform   Include testing/observability tables
#   --help               Show this help message
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONTAINER_NAME="pilot-postgres"
DB_NAME="vitea_db"
DB_USER="dbadmin"
DB_PASSWORD="vitea123"
CLEAN_MODE=false
SCHEMA_ONLY=false
LOAD_SAMPLE_DATA=true
INCLUDE_TESTING=true

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo ""
    echo -e "${CYAN}═══════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}📋 $1${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════${NC}"
}

# Help function
show_help() {
    echo -e "${MAGENTA}╔═══════════════════════════════════════════════════════════╗${NC}"
    echo -e "${MAGENTA}║     Pilot Admin Platform Complete Setup Script            ║${NC}"
    echo -e "${MAGENTA}╚═══════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --container <name>   PostgreSQL container name (default: pilot-postgres)"
    echo "  --database <name>    Database name (default: vitea_db)"
    echo "  --user <name>        Database user (default: dbadmin)"
    echo "  --password <pass>    Database password (default: vitea123)"
    echo "  --clean              Drop and recreate database from scratch"
    echo "  --schema-only        Only create schema, skip sample data"
    echo "  --sample-data        Load HIPAA sample data (default: true)"
    echo "  --testing-platform   Include testing/observability tables (default: true)"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Full setup with all features"
    echo "  $0 --schema-only                      # Schema only, no sample data"
    echo "  $0 --clean                             # Clean install with sample data"
    echo "  $0 --clean --schema-only               # Clean schema only"
    echo "  $0 --container my-postgres             # Use custom container"
    echo ""
    echo "What this script does:"
    echo "  1. Creates complete database schema (30+ tables)"
    echo "  2. Sets up all stored procedures and functions"
    echo "  3. Creates indexes and constraints"
    echo "  4. Loads HIPAA compliance sample data"
    echo "  5. Sets up testing/observability platform tables"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --container)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --database)
            DB_NAME="$2"
            shift 2
            ;;
        --user)
            DB_USER="$2"
            shift 2
            ;;
        --password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        --clean)
            CLEAN_MODE=true
            shift
            ;;
        --schema-only)
            SCHEMA_ONLY=true
            LOAD_SAMPLE_DATA=false
            shift
            ;;
        --sample-data)
            LOAD_SAMPLE_DATA=true
            shift
            ;;
        --no-testing-platform)
            INCLUDE_TESTING=false
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Export password for psql
export PGPASSWORD="$DB_PASSWORD"

# Print banner
print_banner() {
    echo ""
    echo -e "${MAGENTA}╔═══════════════════════════════════════════════════════════╗${NC}"
    echo -e "${MAGENTA}║                                                           ║${NC}"
    echo -e "${MAGENTA}║          🚀 PILOT ADMIN PLATFORM SETUP 🚀                ║${NC}"
    echo -e "${MAGENTA}║                                                           ║${NC}"
    echo -e "${MAGENTA}║            Vitea.ai Policy Management System              ║${NC}"
    echo -e "${MAGENTA}║                                                           ║${NC}"
    echo -e "${MAGENTA}╚═══════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Check if Docker container is running
check_container() {
    log_step "Checking Docker Environment"
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if container exists
    if ! docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_error "Container '${CONTAINER_NAME}' does not exist"
        log_info "Create it with: docker run --name ${CONTAINER_NAME} -e POSTGRES_PASSWORD=${DB_PASSWORD} -p 5432:5432 -d postgres:15"
        exit 1
    fi
    
    # Check if container is running
    if ! docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_warning "Container '${CONTAINER_NAME}' exists but is not running"
        log_info "Starting container..."
        docker start "${CONTAINER_NAME}"
        sleep 3
    fi
    
    log_success "Container '${CONTAINER_NAME}' is running"
}

# Wait for PostgreSQL to be ready
wait_for_postgres() {
    log_info "Waiting for PostgreSQL to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "SELECT 1;" > /dev/null 2>&1; then
            log_success "PostgreSQL is ready"
            return 0
        fi
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    log_error "PostgreSQL did not become ready in time"
    exit 1
}

# Clean existing database if requested
clean_database() {
    if [[ "$CLEAN_MODE" == "true" ]]; then
        log_step "Cleaning Existing Database"
        
        log_warning "This will DROP and RECREATE the database '${DB_NAME}'"
        echo -n "Are you sure? (y/N): "
        read -r confirmation
        
        if [[ "$confirmation" != "y" && "$confirmation" != "Y" ]]; then
            log_info "Cleanup cancelled"
            exit 0
        fi
        
        # Terminate existing connections
        log_info "Terminating existing connections..."
        docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c \
            "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '${DB_NAME}' AND pid <> pg_backend_pid();" > /dev/null 2>&1 || true
        
        # Drop database
        log_info "Dropping database '${DB_NAME}'..."
        docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c \
            "DROP DATABASE IF EXISTS ${DB_NAME};" 2>&1 | grep -v "NOTICE" || true
        
        # Create fresh database
        log_info "Creating fresh database '${DB_NAME}'..."
        docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c \
            "CREATE DATABASE ${DB_NAME};" || {
            log_error "Failed to create database"
            exit 1
        }
        
        log_success "Database cleaned and recreated"
    fi
}

# Test database connection
test_connection() {
    log_step "Testing Database Connection"
    
    if docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" > /dev/null 2>&1; then
        local pg_version=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT version();" | head -1)
        log_success "Connected to database '${DB_NAME}'"
        log_info "PostgreSQL version: ${pg_version}"
    else
        log_error "Failed to connect to database '${DB_NAME}'"
        log_info "Creating database..."
        docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d postgres -c "CREATE DATABASE ${DB_NAME};" || {
            log_error "Failed to create database"
            exit 1
        }
        log_success "Database created"
    fi
}

# Create complete schema
create_schema() {
    log_step "Creating Database Schema"
    
    # Check if schema file exists
    SCHEMA_FILE="${SCRIPT_DIR}/create-complete-schema.sql"
    if [[ ! -f "$SCHEMA_FILE" ]]; then
        log_error "Schema file not found: $SCHEMA_FILE"
        exit 1
    fi
    
    log_info "Applying complete database schema..."
    log_info "This includes:"
    log_info "  • Core tables (users, roles, policies, agents)"
    log_info "  • Policy management tables"
    log_info "  • Audit and compliance tables"
    log_info "  • MCP and chat session tables"
    if [[ "$INCLUDE_TESTING" == "true" ]]; then
        log_info "  • Testing and observability tables"
    fi
    log_info "  • All stored procedures and functions"
    log_info "  • Indexes and constraints"
    
    # Apply schema
    cat "$SCHEMA_FILE" | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" 2>&1 | \
        grep -E "(ERROR)" && {
        log_error "Schema creation had errors"
        exit 1
    } || true
    
    # Verify tables were created
    local table_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';")
    
    log_success "Schema created successfully with $(echo $table_count | xargs) tables"
}

# Load HIPAA sample data
load_hipaa_data() {
    if [[ "$LOAD_SAMPLE_DATA" == "true" ]]; then
        log_step "Loading HIPAA Compliance Sample Data"
        
        # Check if sample data file exists
        HIPAA_DATA_FILE="${SCRIPT_DIR}/hipaa-sample-data-safe.sql"
        if [[ ! -f "$HIPAA_DATA_FILE" ]]; then
            log_error "HIPAA sample data file not found: $HIPAA_DATA_FILE"
            exit 1
        fi
        
        log_info "Loading HIPAA compliance demonstration data..."
        log_info "This includes:"
        log_info "  • 5 Healthcare professional users"
        log_info "  • 4 Security and compliance roles"
        log_info "  • 8 HIPAA compliance policies"
        log_info "  • 2 Policy groups (Critical & Standard)"
        log_info "  • 1 HIPAA compliance agent"
        log_info "  • Sample audit logs and executions"
        
        # Load HIPAA data
        cat "$HIPAA_DATA_FILE" | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" 2>&1 | \
            grep -E "(ERROR)" && {
            log_warning "Some sample data may have had conflicts (this is normal if data already exists)"
        } || true
        
        log_success "HIPAA sample data loaded successfully"
    fi
}

# Load testing platform sample data
load_testing_data() {
    if [[ "$LOAD_SAMPLE_DATA" == "true" ]] && [[ "$INCLUDE_TESTING" == "true" ]]; then
        log_step "Loading Testing Platform Sample Data"
        
        # Check if testing data file exists
        TESTING_DATA_FILE="${SCRIPT_DIR}/testing-observability-sample-data.sql"
        if [[ -f "$TESTING_DATA_FILE" ]]; then
            log_info "Loading testing and observability sample data..."
            log_info "This includes:"
            log_info "  • 8 Evaluation metrics"
            log_info "  • 4 Test datasets"
            log_info "  • 4 Sample experiments"
            log_info "  • Evaluation results"
            
            cat "$TESTING_DATA_FILE" | docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" 2>&1 | \
                grep -E "(ERROR)" && {
                log_warning "Some testing data may have had conflicts (this is normal)"
            } || true
            
            log_success "Testing platform sample data loaded"
        else
            log_info "Testing sample data file not found, skipping"
        fi
    fi
}

# Verify installation
verify_installation() {
    log_step "Verifying Installation"
    
    # Get counts
    local user_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM users;" 2>/dev/null || echo "0")
    local policy_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM policies WHERE deleted_at IS NULL;" 2>/dev/null || echo "0")
    local agent_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM agents WHERE deleted_at IS NULL;" 2>/dev/null || echo "0")
    local role_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
        -c "SELECT COUNT(*) FROM roles;" 2>/dev/null || echo "0")
    
    log_info "Database Statistics:"
    echo -e "  ${GREEN}✓${NC} Users: $(echo $user_count | xargs)"
    echo -e "  ${GREEN}✓${NC} Policies: $(echo $policy_count | xargs)"
    echo -e "  ${GREEN}✓${NC} Agents: $(echo $agent_count | xargs)"
    echo -e "  ${GREEN}✓${NC} Roles: $(echo $role_count | xargs)"
    
    if [[ "$INCLUDE_TESTING" == "true" ]]; then
        local dataset_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
            -c "SELECT COUNT(*) FROM datasets WHERE status = 'active';" 2>/dev/null || echo "0")
        local experiment_count=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t \
            -c "SELECT COUNT(*) FROM experiments;" 2>/dev/null || echo "0")
        
        echo -e "  ${GREEN}✓${NC} Test Datasets: $(echo $dataset_count | xargs)"
        echo -e "  ${GREEN}✓${NC} Experiments: $(echo $experiment_count | xargs)"
    fi
    
    # Test a stored procedure
    log_info "Testing stored procedures..."
    docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c \
        "SELECT COUNT(*) FROM search_policies(NULL, NULL, NULL, NULL, 10, 0);" > /dev/null 2>&1 && \
        echo -e "  ${GREEN}✓${NC} Stored procedures working" || \
        echo -e "  ${RED}✗${NC} Stored procedures may have issues"
}

# Display summary and next steps
display_summary() {
    log_step "Setup Complete!"
    
    echo ""
    echo -e "${GREEN}╔═══════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║         ✅ PILOT ADMIN PLATFORM READY! ✅                 ║${NC}"
    echo -e "${GREEN}╚═══════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${CYAN}Database Configuration:${NC}"
    echo "  • Container: ${CONTAINER_NAME}"
    echo "  • Database: ${DB_NAME}"
    echo "  • User: ${DB_USER}"
    echo "  • Port: 5432"
    echo ""
    
    if [[ "$LOAD_SAMPLE_DATA" == "true" ]]; then
        echo -e "${CYAN}Sample Data Loaded:${NC}"
        echo "  • HIPAA compliance policies and configurations"
        echo "  • Healthcare professional user accounts"
        echo "  • Security roles and permissions"
        echo "  • Audit trail examples"
        if [[ "$INCLUDE_TESTING" == "true" ]]; then
            echo "  • Test datasets and experiments"
            echo "  • Evaluation metrics and results"
        fi
        echo ""
        
        echo -e "${CYAN}Sample Users (password: 'password123'):${NC}"
        echo "  • <EMAIL> (Physician)"
        echo "  • <EMAIL> (Nurse Practitioner)"
        echo "  • <EMAIL> (Nurse)"
        echo "  • <EMAIL> (Medical Assistant)"
        echo "  • <EMAIL> (System Admin)"
        echo ""
    fi
    
    echo -e "${CYAN}Next Steps:${NC}"
    echo "  1. Start the API server:"
    echo "     ${YELLOW}cd enhanced-api-project && npm start${NC}"
    echo ""
    echo "  2. Start the Admin UI:"
    echo "     ${YELLOW}cd admin-ui-project && npm start${NC}"
    echo ""
    echo "  3. Access the applications:"
    echo "     • Admin UI: ${BLUE}http://localhost:3001${NC}"
    echo "     • API: ${BLUE}http://localhost:8001${NC}"
    echo ""
    echo "  4. Explore the sample data:"
    echo "     • View HIPAA compliance policies"
    echo "     • Test policy evaluations"
    echo "     • Review audit logs"
    if [[ "$INCLUDE_TESTING" == "true" ]]; then
        echo "     • Run test experiments"
        echo "     • View evaluation results"
    fi
    echo ""
    echo -e "${GREEN}Happy coding! 🚀${NC}"
    echo ""
}

# Main execution flow
main() {
    print_banner
    
    log_info "Configuration:"
    log_info "  Container: $CONTAINER_NAME"
    log_info "  Database: $DB_NAME"
    log_info "  User: $DB_USER"
    log_info "  Clean Mode: $CLEAN_MODE"
    log_info "  Load Sample Data: $LOAD_SAMPLE_DATA"
    log_info "  Include Testing Platform: $INCLUDE_TESTING"
    
    check_container
    wait_for_postgres
    
    if [[ "$CLEAN_MODE" == "true" ]]; then
        clean_database
    fi
    
    test_connection
    create_schema
    
    if [[ "$SCHEMA_ONLY" == "false" ]]; then
        load_hipaa_data
        load_testing_data
    fi
    
    verify_installation
    display_summary
}

# Handle script termination
trap 'echo -e "\n${RED}Setup interrupted${NC}"; exit 1' INT TERM

# Execute main function
main "$@"