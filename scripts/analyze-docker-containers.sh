#!/bin/bash

# Simple Docker container summary script
# Shows name, ports, network, running status, and uptime

echo "Docker Container Summary"
echo "Generated: $(date)"
echo "========================"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running or not accessible"
    exit 1
fi

if [ -z "$(docker ps -q)" ]; then
    echo "📭 No running containers found"
    exit 0
fi

# Function to get container networks
get_container_networks() {
    local container_id=$1
    docker inspect "$container_id" --format '{{range $k, $v := .NetworkSettings.Networks}}{{$k}} {{end}}' 2>/dev/null | sed 's/ $//'
}

# Function to get simplified port mapping
get_ports() {
    local container_id=$1
    docker inspect "$container_id" --format '{{range $p, $conf := .NetworkSettings.Ports}}{{if $conf}}{{(index $conf 0).HostPort}}:{{$p}}{{else}}{{$p}}{{end}} {{end}}' 2>/dev/null | sed 's/ $//'
}

echo ""
printf "%-35s %-30s %-30s %-10s %-30s\n" "NAME" "PORTS" "NETWORK" "STATUS" "UPTIME"
echo "-------------------------------------------------------------------------------------------------------------------------------"

# Process each running container
for container_id in $(docker ps -q); do
    container_name=$(docker inspect "$container_id" --format '{{.Name}}' | sed 's/^\///')
    container_status=$(docker inspect "$container_id" --format '{{.State.Status}}')
    networks=$(get_container_networks "$container_id")
    ports=$(get_ports "$container_id")
    uptime=$(docker ps --format "{{.Status}}" --filter id="$container_id")
    
    # Truncate name if too long but keep reasonable length
    if [ ${#container_name} -gt 34 ]; then
        container_name=$(echo "$container_name" | cut -c1-31)...
    fi
    
    # Truncate network if too long
    if [ ${#networks} -gt 29 ]; then
        networks=$(echo "$networks" | cut -c1-26)...
    fi
    
    # Split ports into array and display on multiple lines if needed
    if [ -n "$ports" ]; then
        # Convert space-separated ports into array
        ports_array=($ports)
        
        # Display first port with full container info
        printf "%-35s %-30s %-30s %-10s %-30s\n" "$container_name" "${ports_array[0]}" "$networks" "$container_status" "$uptime"
        
        # Display additional ports on separate lines
        for ((i=1; i<${#ports_array[@]}; i++)); do
            printf "%-35s %-30s %-30s %-10s %-30s\n" "" "${ports_array[i]}" "" "" ""
        done
    else
        printf "%-35s %-30s %-30s %-10s %-30s\n" "$container_name" "None" "$networks" "$container_status" "$uptime"
    fi
done

echo ""