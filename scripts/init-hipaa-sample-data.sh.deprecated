#!/bin/bash
set -e

# =============================================================================
# HIPAA Sample Data Initialization Script for Development
# =============================================================================
# Purpose: Populate database with HIPAA compliance demo data
# Usage: ./init-hipaa-sample-data.sh [OPTIONS]
# 
# Options:
#   --env <dev|staging>  Target environment (default: dev)
#   --clean              Remove sample data only
#   --validate-only      Check relationships without changes
#   --help               Show this help message
# =============================================================================

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_DIR="${SCRIPT_DIR}/../configs"
ENVIRONMENT="dev"
CLEAN_ONLY=false
VALIDATE_ONLY=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo ""
    echo -e "${BLUE}📋 STEP: $1${NC}"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        log_success "$1"
    else
        log_error "$1"
        exit 1
    fi
}

# Help function
show_help() {
    echo "HIPAA Sample Data Initialization Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --env <dev|staging>  Target environment (default: dev)"
    echo "  --clean              Remove sample data only"
    echo "  --validate-only      Check relationships without changes"
    echo "  --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                              # Initialize dev environment"
    echo "  $0 --env staging                # Initialize staging environment"
    echo "  $0 --clean                      # Remove sample data only"
    echo "  $0 --validate-only              # Validate data relationships"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --clean)
            CLEAN_ONLY=true
            shift
            ;;
        --validate-only)
            VALIDATE_ONLY=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be 'dev' or 'staging'"
    exit 1
fi

# Safety check for production
if [[ "$ENVIRONMENT" == "prod" ]]; then
    log_error "This script cannot be run against production environment"
    exit 1
fi

# Load environment configuration
ENV_CONFIG_FILE="${CONFIG_DIR}/environment.conf"
if [[ ! -f "$ENV_CONFIG_FILE" ]]; then
    log_error "Environment configuration file not found: $ENV_CONFIG_FILE"
    exit 1
fi

source "$ENV_CONFIG_FILE"

# Database connection parameters
export PGPASSWORD="$DB_PASSWORD"
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-vitea_db}"
DB_USER="${DB_USER:-dbadmin}"

log_info "🏥 HIPAA Sample Data Initialization Script"
log_info "Environment: $ENVIRONMENT"
log_info "Database: $DB_HOST:$DB_PORT/$DB_NAME"
echo "=================================================="

# Function to execute SQL with error handling
execute_sql() {
    local sql_file="$1"
    local description="$2"
    
    if [[ ! -f "$sql_file" ]]; then
        log_error "SQL file not found: $sql_file"
        return 1
    fi
    
    log_info "Executing: $description"
    psql -h "$DB_HOST" \
         -p "$DB_PORT" \
         -d "$DB_NAME" \
         -U "$DB_USER" \
         -f "$sql_file" \
         --set sslmode=require \
         -q
    
    return $?
}

# Function to execute SQL command directly
execute_sql_command() {
    local sql_command="$1"
    local description="$2"
    
    log_info "Executing: $description"
    psql -h "$DB_HOST" \
         -p "$DB_PORT" \
         -d "$DB_NAME" \
         -U "$DB_USER" \
         --set sslmode=require \
         -q \
         -c "$sql_command"
    
    return $?
}

# Function to validate database connection
validate_connection() {
    log_step "Validating database connection"
    execute_sql_command "SELECT 1;" "Database connectivity test"
    check_success "Database connection validated"
}

# Function to clean sample data
clean_sample_data() {
    log_step "Cleaning existing sample data"
    
    # Clean in reverse dependency order to avoid foreign key violations
    local tables=(
        "policy_executions"
        "audit_log" 
        "agent_policies"
        "agent_access"
        "policy_group_policies"
        "user_roles"
        "policy_violations"
        "agents"
        "policies"
        "policy_templates"
        "policy_groups"
        "roles"
        "users"
    )
    
    for table in "${tables[@]}"; do
        execute_sql_command "DELETE FROM $table WHERE 1=1;" "Cleaning table: $table"
        check_success "Cleaned table: $table"
    done
    
    log_success "Sample data cleanup completed"
}

# Function to validate data relationships
validate_data_relationships() {
    log_step "Validating data relationships"
    
    # Check foreign key constraints
    local validation_queries=(
        "SELECT COUNT(*) FROM users;" "Users count"
        "SELECT COUNT(*) FROM roles;" "Roles count"
        "SELECT COUNT(*) FROM user_roles ur JOIN users u ON ur.user_id = u.user_id JOIN roles r ON ur.role_id = r.role_id;" "User-Role relationships"
        "SELECT COUNT(*) FROM agents;" "Agents count"
        "SELECT COUNT(*) FROM policy_templates;" "Policy templates count"
        "SELECT COUNT(*) FROM policies;" "Policies count"
        "SELECT COUNT(*) FROM policy_groups;" "Policy groups count"
        "SELECT COUNT(*) FROM policy_group_policies pgp JOIN policy_groups pg ON pgp.group_id = pg.group_id JOIN policies p ON pgp.policy_id = p.policy_id;" "Policy group relationships"
        "SELECT COUNT(*) FROM agent_policies ap JOIN agents a ON ap.agent_id = a.agent_id JOIN policies p ON ap.policy_id = p.policy_id;" "Agent-Policy relationships"
        "SELECT COUNT(*) FROM agent_access aa JOIN agents a ON aa.agent_id = a.agent_id JOIN roles r ON aa.role_id = r.role_id;" "Agent access relationships"
    )
    
    for ((i=0; i<${#validation_queries[@]}; i+=2)); do
        local query="${validation_queries[i]}"
        local description="${validation_queries[i+1]}"
        
        result=$(psql -h "$DB_HOST" -p "$DB_PORT" -d "$DB_NAME" -U "$DB_USER" --set sslmode=require -t -c "$query" | xargs)
        log_info "$description: $result records"
    done
    
    log_success "Data relationship validation completed"
}

# Function to create backup
create_backup() {
    if [[ "$ENVIRONMENT" != "dev" ]]; then
        log_step "Creating database backup"
        
        BACKUP_DIR="${SCRIPT_DIR}/../backups"
        mkdir -p "$BACKUP_DIR"
        
        BACKUP_FILE="$BACKUP_DIR/vitea_db_backup_hipaa_init_$(date +%Y%m%d_%H%M%S).sql"
        
        pg_dump -h "$DB_HOST" \
                -p "$DB_PORT" \
                -d "$DB_NAME" \
                -U "$DB_USER" \
                --no-password \
                > "$BACKUP_FILE"
        
        check_success "Database backup created: $BACKUP_FILE"
    else
        log_info "Skipping backup for dev environment"
    fi
}

# Main execution flow
main() {
    validate_connection
    
    if [[ "$VALIDATE_ONLY" == "true" ]]; then
        validate_data_relationships
        log_success "Validation completed successfully"
        exit 0
    fi
    
    if [[ "$CLEAN_ONLY" == "true" ]]; then
        clean_sample_data
        log_success "Clean operation completed successfully"
        exit 0
    fi
    
    # Create backup before making changes (except for dev)
    create_backup
    
    # Clean existing sample data
    clean_sample_data
    
    # Initialize with new HIPAA sample data
    log_step "Initializing HIPAA sample data"
    execute_sql "${SCRIPT_DIR}/hipaa-sample-data.sql" "HIPAA sample data initialization"
    check_success "HIPAA sample data initialized"
    
    # Validate the new data
    validate_data_relationships
    
    log_success "🏥 HIPAA sample data initialization completed successfully!"
    echo ""
    log_info "Database is now ready for HIPAA compliance demos"
    log_info "Sample data includes:"
    log_info "  • 5 Healthcare users with realistic roles"
    log_info "  • 8 HIPAA compliance policies with PHI redaction"
    log_info "  • 1 HIPAA compliance agent"
    log_info "  • Complete audit trail and policy execution logs"
    echo ""
}

# Execute main function
main "$@"