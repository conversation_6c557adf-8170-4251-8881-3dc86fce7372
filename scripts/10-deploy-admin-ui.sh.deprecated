#!/bin/bash
set -e

echo "🌐 Deploying Policy Admin UI..."
echo "==============================="

source ../configs/environment.conf

log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_step "Preparing admin UI project"
cd ../admin-ui-project

log_step "Installing admin UI dependencies"
npm install --silent
check_success "Dependencies installed"

log_step "Creating production environment file"
cat > .env.production << EOF
REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net
REACT_APP_APP_NAME=Vitea Policy Admin
EOF
check_success "Environment file created"

log_step "Building admin UI application"
REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net \
npm run build #--silent
check_success "Admin UI build completed"

cd ../scripts

log_step "Creating Static Web App for Admin UI"
ADMIN_SWA_NAME="${COMPANY_NAME}-${ENVIRONMENT}-admin"

az staticwebapp create \
  --name $ADMIN_SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --location "centralus" 
#  --only-show-errors
check_success "Admin Static Web App created"

ADMIN_SWA_URL=$(az staticwebapp show \
  --name $ADMIN_SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "defaultHostname" \
  --output tsv)

echo "Admin UI URL: https://$ADMIN_SWA_URL"

log_step "Getting admin deployment token"
ADMIN_DEPLOYMENT_TOKEN=$(az staticwebapp secrets list \
  --name $ADMIN_SWA_NAME \
  --resource-group $RESOURCE_GROUP \
  --query "properties.apiKey" \
  --output tsv)
check_success "Admin deployment token retrieved"

log_step "Deploying admin UI with SWA CLI"
cd ../admin-ui-project
sudo swa deploy ./build \
  --deployment-token $ADMIN_DEPLOYMENT_TOKEN \
  --app-location "."
check_success "Admin UI deployed"

cd ../scripts

echo "⏱️ Waiting for admin deployment to propagate (30 seconds)..."
sleep 30

log_step "Testing admin UI deployment"
ADMIN_FRONTEND_URL="https://$ADMIN_SWA_URL"
ADMIN_RESPONSE=$(curl -o /dev/null -w "%{http_code}" "$ADMIN_FRONTEND_URL" || echo "000")

if [ "$ADMIN_RESPONSE" = "200" ]; then
    echo "✅ Admin UI accessible (HTTP 200)"
else
    echo "⚠️ Admin UI returned HTTP $ADMIN_RESPONSE (may still be deploying)"
fi

echo "export ADMIN_FRONTEND_URL=\"https://$ADMIN_SWA_URL\"" >> ../configs/environment.conf

echo ""
echo "🎉 Policy Admin UI deployment completed!"
echo "Admin UI URL: https://$ADMIN_SWA_URL"
echo "Login: Use admin credentials to access policy management features"
