#!/bin/bash
set -e

echo "🚀 Vitea Complete System Redeployment Script"
echo "============================================="
echo "This script will redeploy all Vitea components to Azure:"
echo "• Main Application (API + Frontend Container [Admin UI + Chatbot] + MCP Server)"
echo "• FHIR Proxy Stack (Envoy + OPA + Audit + Policy Services)"
echo ""

# Source environment configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/../configs/environment.conf"

# Logging functions
log_step() {
    echo ""
    echo "📋 STEP: $1"
    echo "----------------------------------------"
}

check_success() {
    if [ $? -eq 0 ]; then
        echo "✅ SUCCESS: $1"
    else
        echo "❌ FAILED: $1"
        exit 1
    fi
}

log_info() {
    echo "ℹ️  INFO: $1"
}

# Verify Azure CLI login
log_step "Verifying Azure CLI authentication"
if ! az account show &> /dev/null; then
    echo "❌ Please log in to Azure CLI first: az login"
    exit 1
fi
echo "✅ Azure CLI authenticated"

# Get current subscription info
SUBSCRIPTION_NAME=$(az account show --query name -o tsv)
echo "📍 Using subscription: $SUBSCRIPTION_NAME"
echo "📍 Resource Group: $RESOURCE_GROUP"

# Function to deploy Enhanced API
deploy_api() {
    log_step "Deploying Enhanced API Service"
    cd "${SCRIPT_DIR}/../enhanced-api-project"
    
    log_info "Installing production dependencies"
    npm install --production --silent
    check_success "Dependencies installed"
    
    log_info "Creating deployment package"
    rm -f enhanced-api-deployment.zip
    zip -r enhanced-api-deployment.zip . -x "*.git*" "*.DS_Store" "node_modules/.cache/*" &> /dev/null
    check_success "Deployment package created"
    
    log_info "Uploading to Azure App Service"
    az webapp deployment source config-zip \
      --resource-group $RESOURCE_GROUP \
      --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
      --src enhanced-api-deployment.zip \
      --only-show-errors
    check_success "API code deployed"
    
    log_info "Restarting API service"
    az webapp restart \
      --resource-group $RESOURCE_GROUP \
      --name "${COMPANY_NAME}-${ENVIRONMENT}-api" \
      --only-show-errors
    check_success "API service restarted"
    
    echo "⏱️ Waiting for API to start (30 seconds)..."
    sleep 30
    
    API_URL="https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net"
    log_info "Testing API health at $API_URL"
    HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$API_URL/health" || echo "000")
    
    if [ "$HEALTH_RESPONSE" = "200" ]; then
        echo "✅ API health check passed"
    else
        echo "⚠️ API health check returned HTTP $HEALTH_RESPONSE (may still be starting)"
    fi
    
    cd "${SCRIPT_DIR}"
}

# Function to deploy Frontend
deploy_frontend() {
    log_step "Deploying React Frontend"
    cd "${SCRIPT_DIR}/../frontend-project"
    
    log_info "Installing frontend dependencies"
    npm install --silent
    check_success "Dependencies installed"
    
    log_info "Building React application"
    REACT_APP_AZURE_CLIENT_ID=$APP_ID \
    REACT_APP_AZURE_TENANT_ID=$TENANT_ID \
    REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net \
    npm run build --silent
    check_success "React build completed"
    
    log_info "Getting Static Web App deployment token"
    SWA_NAME="${COMPANY_NAME}-${ENVIRONMENT}-frontend"
    DEPLOYMENT_TOKEN=$(az staticwebapp secrets list \
      --name $SWA_NAME \
      --resource-group $RESOURCE_GROUP \
      --query "properties.apiKey" \
      --output tsv)
    check_success "Deployment token retrieved"
    
    log_info "Deploying to Azure Static Web Apps"
    npx @azure/static-web-apps-cli deploy ./build \
      --deployment-token $DEPLOYMENT_TOKEN \
      --app-location "/" &> /dev/null
    check_success "Frontend deployed"
    
    echo "⏱️ Waiting for frontend deployment to propagate (45 seconds)..."
    sleep 45
    
    FRONTEND_URL="https://$(az staticwebapp show \
      --name $SWA_NAME \
      --resource-group $RESOURCE_GROUP \
      --query "defaultHostname" \
      --output tsv)"
    
    log_info "Testing frontend at $FRONTEND_URL"
    FRONTEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$FRONTEND_URL" || echo "000")
    
    if [ "$FRONTEND_RESPONSE" = "200" ]; then
        echo "✅ Frontend accessible"
    else
        echo "⚠️ Frontend returned HTTP $FRONTEND_RESPONSE (may still be deploying)"
    fi
    
    cd "${SCRIPT_DIR}"
}

# Function to deploy Admin UI
deploy_admin_ui() {
    log_step "Deploying Admin UI"
    cd "${SCRIPT_DIR}/../admin-ui-project"
    
    log_info "Installing admin UI dependencies"
    npm install --silent
    check_success "Dependencies installed"
    
    log_info "Building Admin UI React application"
    REACT_APP_AZURE_CLIENT_ID=$APP_ID \
    REACT_APP_AZURE_TENANT_ID=$TENANT_ID \
    REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net \
    npm run build --silent
    check_success "Admin UI build completed"
    
    log_info "Getting Admin UI Static Web App deployment token"
    ADMIN_SWA_NAME="${COMPANY_NAME}-${ENVIRONMENT}-admin"
    
    # Check if Admin UI Static Web App exists, create if not
    if ! az staticwebapp show --name $ADMIN_SWA_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        log_info "Creating Admin UI Static Web App"
        az staticwebapp create \
          --name $ADMIN_SWA_NAME \
          --resource-group $RESOURCE_GROUP \
          --location "East US 2" \
          --source "https://github.com/placeholder/repo" \
          --branch "main" \
          --app-location "/" \
          --only-show-errors
        check_success "Admin UI Static Web App created"
        
        echo "⏱️ Waiting for Admin UI Static Web App to initialize (60 seconds)..."
        sleep 60
    fi
    
    ADMIN_DEPLOYMENT_TOKEN=$(az staticwebapp secrets list \
      --name $ADMIN_SWA_NAME \
      --resource-group $RESOURCE_GROUP \
      --query "properties.apiKey" \
      --output tsv)
    check_success "Admin UI deployment token retrieved"
    
    log_info "Deploying Admin UI to Azure Static Web Apps"
    npx @azure/static-web-apps-cli deploy ./build \
      --deployment-token $ADMIN_DEPLOYMENT_TOKEN \
      --app-location "/" &> /dev/null
    check_success "Admin UI deployed"
    
    echo "⏱️ Waiting for Admin UI deployment to propagate (45 seconds)..."
    sleep 45
    
    ADMIN_URL="https://$(az staticwebapp show \
      --name $ADMIN_SWA_NAME \
      --resource-group $RESOURCE_GROUP \
      --query "defaultHostname" \
      --output tsv)"
    
    log_info "Testing Admin UI at $ADMIN_URL"
    ADMIN_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$ADMIN_URL" || echo "000")
    
    if [ "$ADMIN_RESPONSE" = "200" ]; then
        echo "✅ Admin UI accessible"
    else
        echo "⚠️ Admin UI returned HTTP $ADMIN_RESPONSE (may still be deploying)"
    fi
    
    cd "${SCRIPT_DIR}"
}

# Function to deploy Frontend Container (Docker to ACR)
deploy_frontend_container() {
    log_step "Deploying Frontend Container via Docker"
    cd "${SCRIPT_DIR}/.."
    
    log_info "Logging into Azure Container Registry"
    az acr login --name viteamcpserveracr
    check_success "ACR login successful"
    
    log_info "Building Docker image for frontend container"
    docker build -f Dockerfile.frontend.dynamic \
      -t viteamcpserveracr.azurecr.io/vitea-frontend:latest \
      --platform linux/amd64 \
      --build-arg REACT_APP_AZURE_CLIENT_ID=$APP_ID \
      --build-arg REACT_APP_AZURE_TENANT_ID=$TENANT_ID \
      --build-arg REACT_APP_API_BASE_URL=https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net \
      .
    check_success "Docker image built"
    
    log_info "Pushing image to Azure Container Registry"
    docker push viteamcpserveracr.azurecr.io/vitea-frontend:latest
    check_success "Image pushed to ACR"
    
    log_info "Checking if frontend container exists"
    CONTAINER_NAME="pilot-frontend-vnet-private"
    
    if az container show --name $CONTAINER_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        log_info "Deleting existing frontend container"
        az container delete --name $CONTAINER_NAME --resource-group $RESOURCE_GROUP --yes
        check_success "Existing container deleted"
        
        echo "⏱️ Waiting for container deletion to complete (30 seconds)..."
        sleep 30
    fi
    
    log_info "Creating new frontend container with updated image"
    ACR_PASSWORD=$(az acr credential show --name viteamcpserveracr --query "passwords[0].value" -o tsv)
    
    az container create \
      --resource-group $RESOURCE_GROUP \
      --name $CONTAINER_NAME \
      --image viteamcpserveracr.azurecr.io/vitea-frontend:latest \
      --registry-login-server viteamcpserveracr.azurecr.io \
      --registry-username viteamcpserveracr \
      --registry-password $ACR_PASSWORD \
      --cpu 0.5 \
      --memory 1.0 \
      --ports 80 \
      --ip-address private \
      --subnet "/subscriptions/7d91d61f-241a-4803-b43c-ea2730d6ec6f/resourceGroups/$RESOURCE_GROUP/providers/Microsoft.Network/virtualNetworks/vitea-policy-vnet/subnets/vitea-policy-subnet" \
      --environment-variables API_URL=http://********:8000 \
      --os-type Linux \
      --only-show-errors
    check_success "Frontend container created"
    
    echo "⏱️ Waiting for container to start (30 seconds)..."
    sleep 30
    
    log_info "Getting new container IP address"
    NEW_CONTAINER_IP=$(az container show --name $CONTAINER_NAME --resource-group $RESOURCE_GROUP --query "ipAddress.ip" --output tsv)
    echo "📍 New container IP: $NEW_CONTAINER_IP"
    
    log_info "Updating Application Gateway backend pool"
    az network application-gateway address-pool update \
      --resource-group $RESOURCE_GROUP \
      --gateway-name vitea-appgw \
      --name appGatewayBackendPool \
      --servers $NEW_CONTAINER_IP \
      --only-show-errors
    check_success "Application Gateway updated"
    
    echo "⏱️ Waiting for Application Gateway to update (30 seconds)..."
    sleep 30
    
    FRONTEND_CONTAINER_URL="https://vitea-pilot-demo.eastus.cloudapp.azure.com"
    log_info "Testing frontend container at $FRONTEND_CONTAINER_URL"
    FRONTEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -k "$FRONTEND_CONTAINER_URL" || echo "000")
    
    if [ "$FRONTEND_RESPONSE" = "200" ]; then
        echo "✅ Frontend container accessible"
        echo "✅ Admin UI available at: $FRONTEND_CONTAINER_URL/admin/"
    else
        echo "⚠️ Frontend container returned HTTP $FRONTEND_RESPONSE (may still be starting)"
    fi
    
    cd "${SCRIPT_DIR}"
}

# Function to deploy MCP Server
deploy_mcp_server() {
    log_step "Deploying MCP Server"
    cd "${SCRIPT_DIR}/../vitea_mcp_server"
    
    log_info "Checking if MCP App Service exists"
    MCP_APP_NAME="${COMPANY_NAME}-${ENVIRONMENT}-mcp"
    
    if ! az webapp show --name $MCP_APP_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        log_info "Creating MCP App Service"
        az webapp create \
          --resource-group $RESOURCE_GROUP \
          --plan "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
          --name $MCP_APP_NAME \
          --runtime "PYTHON:3.11" \
          --only-show-errors
        check_success "MCP App Service created"
        
        log_info "Configuring MCP App Service settings"
        az webapp config appsettings set \
          --resource-group $RESOURCE_GROUP \
          --name $MCP_APP_NAME \
          --settings \
            PORT=8000 \
            PYTHONPATH=/home/<USER>/wwwroot \
          --only-show-errors
        check_success "MCP App Service configured"
    fi
    
    log_info "Creating MCP deployment package"
    rm -f mcp-deployment.zip
    zip -r mcp-deployment.zip . -x "*.git*" "*.DS_Store" "venv/*" "__pycache__/*" &> /dev/null
    check_success "MCP deployment package created"
    
    log_info "Uploading MCP server to Azure"
    az webapp deployment source config-zip \
      --resource-group $RESOURCE_GROUP \
      --name $MCP_APP_NAME \
      --src mcp-deployment.zip \
      --only-show-errors
    check_success "MCP server deployed"
    
    log_info "Restarting MCP service"
    az webapp restart \
      --resource-group $RESOURCE_GROUP \
      --name $MCP_APP_NAME \
      --only-show-errors
    check_success "MCP service restarted"
    
    echo "⏱️ Waiting for MCP server to start (30 seconds)..."
    sleep 30
    
    MCP_URL="https://${MCP_APP_NAME}.azurewebsites.net"
    log_info "Testing MCP server at $MCP_URL"
    MCP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$MCP_URL/health" || echo "000")
    
    if [ "$MCP_RESPONSE" = "200" ]; then
        echo "✅ MCP server health check passed"
    else
        echo "⚠️ MCP server health check returned HTTP $MCP_RESPONSE (may still be starting)"
    fi
    
    cd "${SCRIPT_DIR}"
}

# Function to deploy Envoy Proxy
deploy_envoy() {
    log_step "Deploying Envoy FHIR Proxy"
    cd "${SCRIPT_DIR}/../envoy_fhir_proxy"
    
    ENVOY_APP_NAME="${COMPANY_NAME}-${ENVIRONMENT}-envoy"
    
    if ! az webapp show --name $ENVOY_APP_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        log_info "Creating Envoy App Service"
        az webapp create \
          --resource-group $RESOURCE_GROUP \
          --plan "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
          --name $ENVOY_APP_NAME \
          --deployment-container-image-name "envoyproxy/envoy:v1.34.2" \
          --only-show-errors
        check_success "Envoy App Service created"
    fi
    
    log_info "Creating Envoy deployment package"
    rm -f envoy-deployment.zip
    zip -r envoy-deployment.zip envoy/ -x "*.git*" "*.DS_Store" &> /dev/null
    check_success "Envoy deployment package created"
    
    log_info "Configuring Envoy container settings"
    az webapp config container set \
      --resource-group $RESOURCE_GROUP \
      --name $ENVOY_APP_NAME \
      --docker-custom-image-name "envoyproxy/envoy:v1.34.2" \
      --docker-registry-server-url "https://index.docker.io" \
      --only-show-errors
    check_success "Envoy container configured"
    
    cd "${SCRIPT_DIR}"
}

# Function to deploy OPA (Open Policy Agent)
deploy_opa() {
    log_step "Deploying OPA Policy Service"
    
    OPA_APP_NAME="${COMPANY_NAME}-${ENVIRONMENT}-opa"
    
    if ! az webapp show --name $OPA_APP_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        log_info "Creating OPA App Service"
        az webapp create \
          --resource-group $RESOURCE_GROUP \
          --plan "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
          --name $OPA_APP_NAME \
          --deployment-container-image-name "openpolicyagent/opa:latest-envoy" \
          --only-show-errors
        check_success "OPA App Service created"
        
        log_info "Configuring OPA settings"
        az webapp config appsettings set \
          --resource-group $RESOURCE_GROUP \
          --name $OPA_APP_NAME \
          --settings \
            WEBSITES_PORT=8181 \
          --only-show-errors
        check_success "OPA settings configured"
    fi
    
    log_info "Restarting OPA service"
    az webapp restart \
      --resource-group $RESOURCE_GROUP \
      --name $OPA_APP_NAME \
      --only-show-errors
    check_success "OPA service restarted"
}

# Function to deploy Audit Ingest Service
deploy_audit_ingest() {
    log_step "Deploying Audit Ingest Service"
    cd "${SCRIPT_DIR}/../../envoy_fhir_proxy/services/audit-ingest"
    
    AUDIT_APP_NAME="${COMPANY_NAME}-${ENVIRONMENT}-audit"
    
    if ! az webapp show --name $AUDIT_APP_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        log_info "Creating Audit Ingest App Service"
        az webapp create \
          --resource-group $RESOURCE_GROUP \
          --plan "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
          --name $AUDIT_APP_NAME \
          --runtime "PYTHON:3.11" \
          --only-show-errors
        check_success "Audit Ingest App Service created"
        
        log_info "Configuring Audit Ingest settings"
        az webapp config appsettings set \
          --resource-group $RESOURCE_GROUP \
          --name $AUDIT_APP_NAME \
          --settings \
            PG_DSN="postgresql://fgp_user:fgp_password@${DB_HOST}:5432/fgp" \
            PORT=8000 \
          --only-show-errors
        check_success "Audit Ingest settings configured"
    fi
    
    log_info "Creating Audit Ingest deployment package"
    rm -f audit-deployment.zip
    zip -r audit-deployment.zip . -x "*.git*" "*.DS_Store" "__pycache__/*" &> /dev/null
    check_success "Audit deployment package created"
    
    log_info "Uploading Audit Ingest service"
    az webapp deployment source config-zip \
      --resource-group $RESOURCE_GROUP \
      --name $AUDIT_APP_NAME \
      --src audit-deployment.zip \
      --only-show-errors
    check_success "Audit Ingest service deployed"
    
    log_info "Restarting Audit Ingest service"
    az webapp restart \
      --resource-group $RESOURCE_GROUP \
      --name $AUDIT_APP_NAME \
      --only-show-errors
    check_success "Audit Ingest service restarted"
    
    cd "${SCRIPT_DIR}"
}

# Function to deploy Policy Check Service
deploy_policy_check() {
    log_step "Deploying Policy Check Service"
    cd "${SCRIPT_DIR}/../../envoy_fhir_proxy/services/policy-check"
    
    POLICY_APP_NAME="${COMPANY_NAME}-${ENVIRONMENT}-policy"
    
    if ! az webapp show --name $POLICY_APP_NAME --resource-group $RESOURCE_GROUP &> /dev/null; then
        log_info "Creating Policy Check App Service"
        az webapp create \
          --resource-group $RESOURCE_GROUP \
          --plan "${COMPANY_NAME}-${ENVIRONMENT}-plan" \
          --name $POLICY_APP_NAME \
          --runtime "PYTHON:3.11" \
          --only-show-errors
        check_success "Policy Check App Service created"
        
        log_info "Configuring Policy Check settings"
        az webapp config appsettings set \
          --resource-group $RESOURCE_GROUP \
          --name $POLICY_APP_NAME \
          --settings \
            PG_DSN="postgresql://fgp_user:fgp_password@${DB_HOST}:5432/fgp" \
            ADMIN_API_KEY="admin-secret-key-123" \
            PORT=8000 \
          --only-show-errors
        check_success "Policy Check settings configured"
    fi
    
    log_info "Creating Policy Check deployment package"
    rm -f policy-deployment.zip
    zip -r policy-deployment.zip . -x "*.git*" "*.DS_Store" "__pycache__/*" &> /dev/null
    check_success "Policy deployment package created"
    
    log_info "Uploading Policy Check service"
    az webapp deployment source config-zip \
      --resource-group $RESOURCE_GROUP \
      --name $POLICY_APP_NAME \
      --src policy-deployment.zip \
      --only-show-errors
    check_success "Policy Check service deployed"
    
    log_info "Restarting Policy Check service"
    az webapp restart \
      --resource-group $RESOURCE_GROUP \
      --name $POLICY_APP_NAME \
      --only-show-errors
    check_success "Policy Check service restarted"
    
    echo "⏱️ Waiting for Policy Check service to start (30 seconds)..."
    sleep 30
    
    POLICY_URL="https://${POLICY_APP_NAME}.azurewebsites.net"
    log_info "Testing Policy Check service at $POLICY_URL"
    POLICY_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$POLICY_URL/health" || echo "000")
    
    if [ "$POLICY_RESPONSE" = "200" ]; then
        echo "✅ Policy Check service health check passed"
    else
        echo "⚠️ Policy Check service health check returned HTTP $POLICY_RESPONSE"
    fi
    
    cd "${SCRIPT_DIR}"
}

# Main deployment flow
main() {
    echo "🎯 Starting complete system redeployment..."
    echo ""
    
    # Ask user what to deploy
    echo "What would you like to redeploy?"
    echo "1) Complete System (All components)"
    echo "2) Main Application Only (API + Frontend Container + MCP)"
    echo "3) FHIR Proxy Stack Only (Envoy + OPA + Audit + Policy)"
    echo "4) Frontend Container Only (Docker deployment with Admin UI + Chatbot)"
    echo "5) Individual Component Selection"
    echo ""
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            echo "🚀 Deploying complete system..."
            deploy_api
            deploy_frontend_container
            deploy_mcp_server
            deploy_opa
            deploy_audit_ingest
            deploy_policy_check
            echo "⚠️ Note: Envoy proxy requires container registry setup - skipping for now"
            ;;
        2)
            echo "🚀 Deploying main application..."
            deploy_api
            deploy_frontend_container
            deploy_mcp_server
            ;;
        3)
            echo "🚀 Deploying FHIR proxy stack..."
            deploy_opa
            deploy_audit_ingest
            deploy_policy_check
            echo "⚠️ Note: Envoy proxy requires container registry setup - skipping for now"
            ;;
        4)
            echo "🚀 Deploying frontend container..."
            deploy_frontend_container
            ;;
        5)
            echo "Select components to deploy:"
            echo "a) API"
            echo "b) Frontend"
            echo "c) Admin UI"
            echo "d) Frontend Container (Docker)"
            echo "e) MCP Server"
            echo "f) OPA Policy Service"
            echo "g) Audit Ingest Service"
            echo "h) Policy Check Service"
            echo ""
            read -p "Enter letters separated by spaces (e.g., 'a b c'): " components
            
            for component in $components; do
                case $component in
                    a) deploy_api ;;
                    b) deploy_frontend ;;
                    c) deploy_admin_ui ;;
                    d) deploy_frontend_container ;;
                    e) deploy_mcp_server ;;
                    f) deploy_opa ;;
                    g) deploy_audit_ingest ;;
                    h) deploy_policy_check ;;
                    *) echo "⚠️ Unknown component: $component" ;;
                esac
            done
            ;;
        *)
            echo "❌ Invalid choice. Exiting."
            exit 1
            ;;
    esac
    
    echo ""
    echo "🎉 Redeployment completed successfully!"
    echo ""
    echo "📋 Service URLs:"
    echo "• API: https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net"
    echo "• Frontend Container: https://vitea-pilot-demo.eastus.cloudapp.azure.com"
    echo "• Admin UI: https://vitea-pilot-demo.eastus.cloudapp.azure.com/admin/"
    echo "• Chatbot UI: https://vitea-pilot-demo.eastus.cloudapp.azure.com/"
    echo "• MCP Server: https://${COMPANY_NAME}-${ENVIRONMENT}-mcp.azurewebsites.net"
    echo "• OPA Policy: https://${COMPANY_NAME}-${ENVIRONMENT}-opa.azurewebsites.net"
    echo "• Audit Ingest: https://${COMPANY_NAME}-${ENVIRONMENT}-audit.azurewebsites.net"
    echo "• Policy Check: https://${COMPANY_NAME}-${ENVIRONMENT}-policy.azurewebsites.net"
    echo ""
    echo "🔍 Health Check Commands:"
    echo "curl https://${COMPANY_NAME}-${ENVIRONMENT}-api.azurewebsites.net/health"
    echo "curl https://${COMPANY_NAME}-${ENVIRONMENT}-mcp.azurewebsites.net/health"
    echo "curl https://${COMPANY_NAME}-${ENVIRONMENT}-policy.azurewebsites.net/health"
}

# Run main function
main "$@"