#!/bin/bash

# ==============================================================================
# Template Management System - Emergency Rollback Script
# ==============================================================================
# This script performs an emergency rollback of the template management system
# deployment, restoring the database to its pre-migration state
# ==============================================================================

set -e  # Exit on error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST=${DB_HOST:-"localhost"}
DB_PORT=${DB_PORT:-"5432"}
DB_NAME=${DB_NAME:-"vitea_db"}
DB_USER=${DB_USER:-"dbadmin"}
BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
ROLLBACK_LOG="rollback_${TIMESTAMP}.log"

# ==============================================================================
# Helper Functions
# ==============================================================================

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1" | tee -a "$ROLLBACK_LOG"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$ROLLBACK_LOG"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$ROLLBACK_LOG"
}

log_section() {
    echo -e "\n${BLUE}==============================================================================
$1
==============================================================================${NC}" | tee -a "$ROLLBACK_LOG"
}

confirm_action() {
    echo -e "${RED}⚠️  WARNING: $1${NC}"
    read -p "$(echo -e ${YELLOW}"Are you sure you want to continue? (yes/no): "${NC})" -r
    echo
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log_warning "Rollback cancelled by user"
        exit 1
    fi
}

# ==============================================================================
# Pre-rollback Checks
# ==============================================================================

log_section "EMERGENCY ROLLBACK PROCEDURE"
log_warning "Starting rollback procedure at $(date)"

confirm_action "This will ROLLBACK the template management system deployment and restore the database!"

# Check for backup files
log_info "Checking for available backups..."

if [ ! -d "$BACKUP_DIR" ]; then
    log_error "Backup directory not found: $BACKUP_DIR"
    exit 1
fi

# List available backups
BACKUPS=($(ls -1 "$BACKUP_DIR"/vitea_db_pre_template_migration_*.sql 2>/dev/null))

if [ ${#BACKUPS[@]} -eq 0 ]; then
    log_error "No backup files found in $BACKUP_DIR"
    log_error "Cannot proceed with rollback without a backup"
    exit 1
fi

echo "Available backups:"
for i in "${!BACKUPS[@]}"; do
    echo "  $((i+1)). ${BACKUPS[$i]}"
done

# Select backup
if [ ${#BACKUPS[@]} -eq 1 ]; then
    SELECTED_BACKUP="${BACKUPS[0]}"
    log_info "Using backup: $SELECTED_BACKUP"
else
    read -p "Select backup number (1-${#BACKUPS[@]}): " BACKUP_NUM
    if [ "$BACKUP_NUM" -lt 1 ] || [ "$BACKUP_NUM" -gt ${#BACKUPS[@]} ]; then
        log_error "Invalid selection"
        exit 1
    fi
    SELECTED_BACKUP="${BACKUPS[$((BACKUP_NUM-1))]}"
fi

# ==============================================================================
# Step 1: Stop Services
# ==============================================================================

log_section "Step 1: Stopping Services"

log_info "Stopping API services to prevent database access..."

# Check if running in Docker
if docker ps | grep -q pilot-api; then
    log_info "Stopping pilot-api container..."
    docker stop pilot-api || log_warning "Failed to stop pilot-api"
else
    log_warning "pilot-api container not found or already stopped"
fi

# ==============================================================================
# Step 2: Create Current State Backup
# ==============================================================================

log_section "Step 2: Creating Current State Backup"

log_info "Creating backup of current state before rollback..."

CURRENT_BACKUP="${BACKUP_DIR}/vitea_db_pre_rollback_${TIMESTAMP}.sql"

if docker exec pilot-postgres pg_dump -U "$DB_USER" -d "$DB_NAME" > "$CURRENT_BACKUP" 2>/dev/null; then
    log_info "✓ Current state backed up to: $CURRENT_BACKUP"
else
    log_warning "Using direct psql backup..."
    if PGPASSWORD=$DB_PASSWORD pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > "$CURRENT_BACKUP"; then
        log_info "✓ Current state backed up to: $CURRENT_BACKUP"
    else
        log_error "Failed to create current state backup"
        confirm_action "Continue without current state backup?"
    fi
fi

# ==============================================================================
# Step 3: Drop Current Database
# ==============================================================================

log_section "Step 3: Preparing Database for Restore"

log_info "Disconnecting active sessions..."

PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "
    SELECT pg_terminate_backend(pid) 
    FROM pg_stat_activity 
    WHERE datname = '$DB_NAME' 
    AND pid <> pg_backend_pid();
" > /dev/null 2>&1 || true

# ==============================================================================
# Step 4: Restore Database
# ==============================================================================

log_section "Step 4: Restoring Database from Backup"

log_info "Restoring database from: $SELECTED_BACKUP"
log_info "This may take several minutes..."

# Check if using Docker or direct connection
if docker ps | grep -q pilot-postgres; then
    # Copy backup file to container
    docker cp "$SELECTED_BACKUP" pilot-postgres:/tmp/restore.sql
    
    # Restore via Docker
    if docker exec pilot-postgres psql -U "$DB_USER" -d "$DB_NAME" -f /tmp/restore.sql > restore_output.log 2>&1; then
        log_info "✓ Database restored successfully"
    else
        log_error "Database restore failed. Check restore_output.log for details"
        tail -20 restore_output.log
        exit 1
    fi
    
    # Clean up
    docker exec pilot-postgres rm /tmp/restore.sql
else
    # Direct restore
    if PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" < "$SELECTED_BACKUP" > restore_output.log 2>&1; then
        log_info "✓ Database restored successfully"
    else
        log_error "Database restore failed. Check restore_output.log for details"
        tail -20 restore_output.log
        exit 1
    fi
fi

# ==============================================================================
# Step 5: Verify Rollback
# ==============================================================================

log_section "Step 5: Verifying Rollback"

log_info "Checking database state after rollback..."

# Check if template columns are removed
COLUMNS_CHECK=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.columns 
    WHERE table_name = 'policy_schemas' 
    AND column_name IN ('default_template', 'template_source');
" | tr -d ' ')

if [ "$COLUMNS_CHECK" = "0" ]; then
    log_info "✓ Template columns removed from policy_schemas"
else
    log_warning "Template columns still exist (may be in backup)"
fi

# Check if policy_templates table exists
TEMPLATES_TABLE=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
    SELECT COUNT(*) 
    FROM information_schema.tables 
    WHERE table_name = 'policy_templates';
" | tr -d ' ')

if [ "$TEMPLATES_TABLE" = "1" ]; then
    log_info "✓ policy_templates table restored"
    
    # Count templates
    TEMPLATE_COUNT=$(PGPASSWORD=$DB_PASSWORD psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "
        SELECT COUNT(*) FROM policy_templates;
    " | tr -d ' ')
    
    log_info "  Found $TEMPLATE_COUNT templates in restored table"
else
    log_warning "policy_templates table not found (check if it existed in backup)"
fi

# ==============================================================================
# Step 6: Restart Services
# ==============================================================================

log_section "Step 6: Restarting Services"

log_info "Starting API services..."

if docker ps -a | grep -q pilot-api; then
    docker start pilot-api
    sleep 5
    
    # Check if service is running
    if docker ps | grep -q pilot-api; then
        log_info "✓ pilot-api restarted successfully"
    else
        log_error "pilot-api failed to start"
        docker logs pilot-api --tail 20
    fi
else
    log_warning "pilot-api container not found. Manual restart required."
fi

# ==============================================================================
# Step 7: Test Basic Functionality
# ==============================================================================

log_section "Step 7: Testing Basic Functionality"

log_info "Testing API health endpoint..."

if curl -s -f "http://localhost:8001/health" > /dev/null 2>&1; then
    log_info "✓ API is responding"
else
    log_error "API is not responding. Check logs for details."
fi

log_info "Testing policy creation (using old system)..."

POLICY_TEST=$(curl -s -X POST "http://localhost:8001/api/v1/policies" \
    -H "Authorization: Bearer admin-token" \
    -H "Content-Type: application/json" \
    -d '{
        "name": "Rollback Test Policy",
        "description": "Testing rollback functionality",
        "policy_type": "medical_privacy",
        "category": "Test",
        "definition": {}
    }')

if echo "$POLICY_TEST" | grep -q '"id"'; then
    log_info "✓ Policy creation working with rolled-back system"
    
    # Clean up test policy
    POLICY_ID=$(echo "$POLICY_TEST" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
    curl -s -X DELETE "http://localhost:8001/api/v1/policies/$POLICY_ID" \
        -H "Authorization: Bearer admin-token" > /dev/null
else
    log_warning "Policy creation may have issues. Manual verification required."
fi

# ==============================================================================
# Step 8: Code Rollback Instructions
# ==============================================================================

log_section "Step 8: Code Rollback Instructions"

cat << EOF | tee -a "$ROLLBACK_LOG"

CODE ROLLBACK REQUIRED
======================
The database has been rolled back, but code changes may still be in place.

To complete the rollback, you need to:

1. Revert Git Changes:
   git checkout main  # or your stable branch
   git pull origin main
   
   Or restore from backup branch:
   git checkout backup/pre-template-migration

2. Rebuild Services:
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d

3. Verify Frontend:
   - Check that policy creation works in UI
   - Verify templates load correctly
   - Test all critical workflows

4. File Cleanup (if needed):
   - Remove: enhanced-api-project/src/services/templateGenerationService.js
   - Remove: enhanced-api-project/src/api/schemaTemplates.js
   - Restore original: enhanced-api-project/src/utils/schemaValidator.js
   - Restore original: admin-ui-project/src/utils/schemaUtils.js

5. Documentation:
   - Document the rollback reason
   - Update deployment status
   - Notify team members

EOF

# ==============================================================================
# Rollback Summary
# ==============================================================================

log_section "ROLLBACK SUMMARY"

cat << EOF | tee -a "$ROLLBACK_LOG"

ROLLBACK COMPLETED
==================
Rollback completed at: $(date)

Database State:
- Restored from: $SELECTED_BACKUP
- Current backup saved: $CURRENT_BACKUP
- Rollback log: $ROLLBACK_LOG

Service Status:
- API: $(docker ps | grep -q pilot-api && echo "Running" || echo "Stopped")
- Database: $(docker ps | grep -q pilot-postgres && echo "Running" || echo "Stopped")

IMPORTANT NEXT STEPS:
1. Follow code rollback instructions above
2. Test all critical functionality
3. Monitor system for stability
4. Document rollback reason and issues encountered

For re-deployment after fixing issues:
1. Review deployment logs from failed attempt
2. Fix identified issues
3. Run: ./scripts/deploy-template-management.sh

EOF

if [ -f "$ROLLBACK_LOG" ]; then
    log_info "Rollback completed. Review log at: $ROLLBACK_LOG"
else
    log_info "Rollback completed."
fi

log_warning "Remember to complete CODE ROLLBACK steps manually!"

exit 0