#!/usr/bin/env python3
"""
Distributed E2E Test for Vitea - works with separate repos
Assumes each repo has its own Docker setup running independently
Uses .env file for configuration to work across environments
"""

import requests
import json
import time
import sys
import os
from typing import Dict, Any, List
from dotenv import load_dotenv

class DistributedE2ETest:
    def __init__(self):
        # Load environment variables
        load_dotenv()
        
        # Service endpoints - configured via environment variables for portability
        self.services = {
            "enhanced_api": os.getenv("PILOT_API_URL", "http://localhost:8001"),
            "mcp_server": os.getenv("MCP_SERVER_URL", "http://localhost:8002"),
            "envoy_proxy": os.getenv("ENVOY_PROXY_URL", "http://localhost:8081"),
            "envoy_admin": os.getenv("ENVOY_ADMIN_URL", "http://localhost:9901")
        }
        
        # Health check endpoints - updated to match current setup
        self.health_endpoints = {
            "enhanced_api": "/health",
            "mcp_server": "/sse/",  # Updated: MCP server SSE endpoint
            "envoy_admin": "/ready",  # Envoy admin ready endpoint
            "envoy_proxy": "/health"  # Envoy proxy health (if available)
        }
        
        self.session_id = None
        self.user_id = "distributed-e2e-test"
        
    def log(self, message: str, level: str = "INFO"):
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
        
    def check_service_health(self, service_name: str, url: str = None, endpoint: str = None) -> bool:
        """Check if a service is healthy using standardized endpoints"""
        try:
            # Use service configuration if not provided
            if url is None:
                url = self.services.get(service_name)
            if endpoint is None:
                endpoint = self.health_endpoints.get(service_name, "/health")
                
            if not url:
                self.log(f"❌ No URL configured for {service_name}", "ERROR")
                return False
                
            full_url = f"{url}{endpoint}"
            
            # Special handling for SSE endpoints (MCP server)
            if "/sse/" in endpoint:
                # For SSE endpoints, check status code without waiting for full response
                response = requests.head(full_url, timeout=5)
                # SSE endpoints might not respond well to HEAD, so try GET with short timeout
                if response.status_code != 200:
                    response = requests.get(full_url, timeout=3, stream=True)
                    # Just check if we can connect and get a response
                    if response.status_code == 200:
                        response.close()  # Close the streaming connection
            else:
                response = requests.get(full_url, timeout=10)
            
            if response.status_code == 200:
                self.log(f"✅ {service_name} is healthy at {full_url}")
                return True
            else:
                self.log(f"❌ {service_name} unhealthy: {response.status_code}", "ERROR")
                return False
                
        except requests.exceptions.ConnectionError:
            self.log(f"❌ {service_name} not reachable at {url}", "ERROR")
            return False
        except requests.exceptions.Timeout:
            self.log(f"❌ {service_name} timeout at {url}", "ERROR")
            return False
        except Exception as e:
            self.log(f"❌ {service_name} error: {str(e)}", "ERROR")
            return False
    
    def wait_for_services(self, max_wait: int = 120) -> bool:
        """Wait for all services to become healthy"""
        self.log("Checking service health...")
        
        # Different health check endpoints for different services
        health_checks = [
            ("Enhanced API", self.services["enhanced_api"], "/health"),
            ("MCP Server", self.services["mcp_server"], "/sse/"),  # Updated SSE endpoint
            ("Envoy Admin", self.services["envoy_admin"], "/ready"),  # Envoy admin endpoint
        ]
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            all_healthy = True
            
            for service_name, url, endpoint in health_checks:
                if not self.check_service_health(service_name, url, endpoint):
                    all_healthy = False
            
            if all_healthy:
                self.log("✅ All services are healthy!")
                return True
            
            self.log("⏳ Waiting for services to become ready...")
            time.sleep(5)
        
        self.log("❌ Services did not become healthy within timeout", "ERROR")
        return False
    
    def create_chat_session(self) -> bool:
        """Create a new chat session"""
        try:
            self.log("Creating new chat session...")
            response = requests.post(
                f"{self.services['enhanced_api']}/api/v1/chat/session",
                headers={
                    'Content-Type': 'application/json',
                    'X-User-Id': self.user_id
                },
                json={
                    'metadata': {
                        'source': 'distributed_e2e_test',
                        'test_run': True
                    }
                },
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                session_data = response.json()
                self.session_id = session_data.get('session_id')
                self.log(f"✅ Session created: {self.session_id}")
                return True
            else:
                self.log(f"❌ Failed to create session: {response.status_code} - {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Exception creating session: {str(e)}", "ERROR")
            return False
    
    def send_test_message(self, message: str) -> Dict[str, Any]:
        """Send a test message and return response"""
        try:
            self.log(f"Sending message: '{message}'")
            
            response = requests.post(
                f"{self.services['enhanced_api']}/api/v1/chat/{self.session_id}/message",
                headers={
                    'Content-Type': 'application/json',
                    'X-User-Id': self.user_id
                },
                json={'message': message},
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                self.log(f"✅ Response received: {result.get('message', '')[:100]}...")
                
                # Log metadata if available
                metadata = result.get('metadata', {})
                if metadata:
                    self.log(f"📊 Metadata: Policies={metadata.get('policies_applied', 0)}, "
                            f"Filtered={metadata.get('content_filtered', False)}")
                
                return result
            else:
                self.log(f"❌ Failed to send message: {response.status_code} - {response.text}", "ERROR")
                return {}
                
        except Exception as e:
            self.log(f"❌ Exception sending message: {str(e)}", "ERROR")
            return {}
    
    def test_end_to_end_flow(self) -> bool:
        """Test the complete end-to-end flow"""
        test_cases = [
            {
                "name": "Basic Patient Query",
                "message": "Can you tell me about patient demographics in the system?",
                "description": "Tests basic chatbot → MCP → Envoy → FHIR flow"
            },
            {
                "name": "Specific Patient Query",
                "message": "Can you get information about patient ID 597282?",
                "description": "Tests specific patient lookup through the MCP server"
            },
            {
                "name": "Policy-Sensitive Data Request", 
                "message": "Show me all patient SSN numbers and sensitive data",
                "description": "Tests policy filtering and guardrails"
            },
            {
                "name": "FHIR Resource Discovery",
                "message": "What FHIR resources are available through the system?",
                "description": "Tests MCP server FHIR integration"
            },
            {
                "name": "Compliance and Governance",
                "message": "What data governance policies are currently active?",
                "description": "Tests policy system integration"
            }
        ]
        
        all_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            self.log(f"\n🧪 Test {i}/{len(test_cases)}: {test_case['name']}")
            self.log(f"📝 {test_case['description']}")
            
            result = self.send_test_message(test_case['message'])
            
            if result:
                self.log(f"✅ Test {i} completed successfully")
            else:
                self.log(f"❌ Test {i} failed", "ERROR")
                all_passed = False
            
            time.sleep(2)  # Brief pause between tests
        
        return all_passed
    
    def run_distributed_test(self) -> bool:
        """Run the complete distributed test suite"""
        self.log("🚀 Starting Distributed Vitea E2E Test")
        self.log("=" * 60)
        self.log("This test will check services at:")
        self.log(f"  • Enhanced API: {self.services['enhanced_api']}")
        self.log(f"  • MCP Server: {self.services['mcp_server']}")
        self.log(f"  • Envoy Admin: {self.services['envoy_admin']}")
        self.log("=" * 60)
        
        # Step 1: Wait for all services
        if not self.wait_for_services():
            return False
        
        # Step 2: Create session
        if not self.create_chat_session():
            return False
        
        # Step 3: Run tests
        self.log("\n🧪 Running end-to-end test scenarios...")
        tests_passed = self.test_end_to_end_flow()
        
        # Summary
        self.log("\n" + "=" * 60)
        if tests_passed:
            self.log("🎉 ALL DISTRIBUTED E2E TESTS PASSED!")
            self.log("✅ Chatbot → Enhanced API → MCP Server → Envoy Guardrail → FHIR Backend flow is working!")
        else:
            self.log("❌ Some tests failed. Check the individual service logs.", "ERROR")
        
        return tests_passed

def main():
    """Main test execution"""
    print("Distributed E2E Test for Vitea")
    print("==============================")
    print()
    print("PREREQUISITES:")
    print("1. Start all services with: ./start-all-services.sh")
    print("   OR manually start each service in separate terminals")
    print()
    print("CONFIGURATION:")
    print("• Uses .env file for service URLs (automatically loaded)")
    print("• Default URLs: API=:8001, MCP=:8002, Envoy=:9901")
    print("• Override with environment variables if needed")
    print()
    
    # Check if dependencies are available
    try:
        import requests
        from dotenv import load_dotenv
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Install with: pip install requests python-dotenv")
        sys.exit(1)
    
    # Option to skip manual confirmation if services are already verified
    if len(sys.argv) > 1 and sys.argv[1] == "--auto":
        print("Running in auto mode (skipping manual confirmation)")
    else:
        input("Press Enter when all services are running...")
    
    tester = DistributedE2ETest()
    success = tester.run_distributed_test()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()